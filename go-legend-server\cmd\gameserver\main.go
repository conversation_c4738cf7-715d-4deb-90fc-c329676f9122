package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/legend-server/go-legend-server/internal/config"
	"github.com/legend-server/go-legend-server/internal/database"
	"github.com/legend-server/go-legend-server/internal/game"
	"github.com/legend-server/go-legend-server/internal/network"
	"github.com/legend-server/go-legend-server/internal/protocol"
	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

var (
	configFile = flag.String("config", "configs/gameserver.yaml", "配置文件路径")
	version    = "1.0.0"
	buildTime  = "unknown"
	gitCommit  = "unknown"
)

func main() {
	flag.Parse()

	// 打印版本信息
	fmt.Printf("Go Legend Server v%s\n", version)
	fmt.Printf("Build Time: %s\n", buildTime)
	fmt.Printf("Git Commit: %s\n", gitCommit)
	fmt.Println()

	// 加载配置
	cfg, err := config.Load(*configFile)
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := logger.Init(&cfg.Log); err != nil {
		fmt.Printf("Failed to init logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	log := logger.GetLogger()
	log.Info("Starting game server",
		zap.String("version", version),
		zap.String("config", *configFile))

	// 创建应用
	app := NewGameServerApp(cfg)

	// 启动应用
	if err := app.Start(); err != nil {
		log.Fatal("Failed to start application", zap.Error(err))
	}

	// 等待信号
	waitForSignal()

	// 停止应用
	if err := app.Stop(); err != nil {
		log.Error("Failed to stop application", zap.Error(err))
	}

	log.Info("Game server stopped")
}

// GameServerApp 游戏服务器应用
type GameServerApp struct {
	config    *config.Config
	database  *database.Database
	server    *network.Server
	gameWorld *game.GameWorld
	logger    *zap.Logger
}

// NewGameServerApp 创建游戏服务器应用
func NewGameServerApp(cfg *config.Config) *GameServerApp {
	return &GameServerApp{
		config: cfg,
		logger: logger.GetLogger(),
	}
}

// Start 启动应用
func (app *GameServerApp) Start() error {
	app.logger.Info("Initializing game server...")

	// 初始化数据库
	if err := app.initDatabase(); err != nil {
		return fmt.Errorf("failed to init database: %w", err)
	}

	// 初始化游戏世界
	if err := app.initGameWorld(); err != nil {
		return fmt.Errorf("failed to init game world: %w", err)
	}

	// 初始化网络服务器
	if err := app.initServer(); err != nil {
		return fmt.Errorf("failed to init server: %w", err)
	}

	// 启动网络服务器
	if err := app.server.Start(); err != nil {
		return fmt.Errorf("failed to start server: %w", err)
	}

	app.logger.Info("Game server started successfully",
		zap.String("name", app.config.Server.Name),
		zap.Int("id", app.config.Server.ID),
		zap.String("address", fmt.Sprintf("%s:%d", app.config.Server.Host, app.config.Server.Port)))

	return nil
}

// Stop 停止应用
func (app *GameServerApp) Stop() error {
	app.logger.Info("Stopping game server...")

	// 停止网络服务器
	if app.server != nil {
		if err := app.server.Stop(); err != nil {
			app.logger.Error("Failed to stop server", zap.Error(err))
		}
	}

	// 停止游戏世界
	if app.gameWorld != nil {
		if err := app.gameWorld.Stop(); err != nil {
			app.logger.Error("Failed to stop game world", zap.Error(err))
		}
	}

	// 关闭数据库
	if app.database != nil {
		if err := app.database.Close(); err != nil {
			app.logger.Error("Failed to close database", zap.Error(err))
		}
	}

	return nil
}

// initDatabase 初始化数据库
func (app *GameServerApp) initDatabase() error {
	app.logger.Info("Initializing database...")

	app.database = database.NewDatabase(&app.config.Database)
	if err := app.database.Connect(); err != nil {
		return err
	}

	// 自动迁移数据库表
	if err := app.database.AutoMigrate(
		&game.Player{},
		// 添加其他模型...
	); err != nil {
		return err
	}

	app.logger.Info("Database initialized successfully")
	return nil
}

// initGameWorld 初始化游戏世界
func (app *GameServerApp) initGameWorld() error {
	app.logger.Info("Initializing game world...")

	app.gameWorld = game.NewGameWorld(&app.config.Game, app.database)
	if err := app.gameWorld.Start(); err != nil {
		return err
	}

	app.logger.Info("Game world initialized successfully")
	return nil
}

// initServer 初始化网络服务器
func (app *GameServerApp) initServer() error {
	app.logger.Info("Initializing network server...")

	// 创建消息处理器
	handler := NewMessageHandler(app.gameWorld, app.database)

	// 创建网络服务器
	app.server = network.NewServer(&app.config.Server, handler)

	app.logger.Info("Network server initialized successfully")
	return nil
}

// MessageHandler 消息处理器
type MessageHandler struct {
	gameWorld *game.GameWorld
	database  *database.Database
	logger    *zap.Logger
}

// NewMessageHandler 创建消息处理器
func NewMessageHandler(gameWorld *game.GameWorld, db *database.Database) *MessageHandler {
	return &MessageHandler{
		gameWorld: gameWorld,
		database:  db,
		logger:    logger.GetLogger(),
	}
}

// HandleMessage 处理消息
func (h *MessageHandler) HandleMessage(session *network.Session, msg protocol.Message) error {
	h.logger.Debug("Handling message",
		zap.Uint64("session_id", session.ID()),
		zap.String("msg_type", fmt.Sprintf("%T", msg)))

	// TODO: 实现具体的消息处理逻辑
	switch m := msg.(type) {
	case *protocol.AuthenticateRequest:
		return h.handleAuthenticate(session, m)
	case *protocol.ChatMessage:
		return h.handleChat(session, m)
	case *protocol.MoveMessage:
		return h.handleMove(session, m)
	case *protocol.AttackMessage:
		return h.handleAttack(session, m)
	default:
		h.logger.Warn("Unknown message type", zap.String("type", fmt.Sprintf("%T", msg)))
	}

	return nil
}

// OnSessionConnected 会话连接事件
func (h *MessageHandler) OnSessionConnected(session *network.Session) error {
	h.logger.Info("Session connected",
		zap.Uint64("session_id", session.ID()),
		zap.String("remote", session.RemoteAddr()))

	// 设置会话状态
	session.SetState(network.SessionStateConnected)

	return nil
}

// OnSessionDisconnected 会话断开事件
func (h *MessageHandler) OnSessionDisconnected(session *network.Session) error {
	h.logger.Info("Session disconnected",
		zap.Uint64("session_id", session.ID()))

	// 处理玩家下线
	if playerData, ok := session.GetUserData("player"); ok {
		if player, ok := playerData.(*game.Player); ok {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			// 保存玩家数据
			if err := player.Save(ctx, h.database); err != nil {
				h.logger.Error("Failed to save player data",
					zap.Uint64("player_id", player.ID),
					zap.Error(err))
			}

			// 从游戏世界移除玩家
			h.gameWorld.RemovePlayer(player.ID)
		}
	}

	return nil
}

// handleAuthenticate 处理认证
func (h *MessageHandler) handleAuthenticate(session *network.Session, msg *protocol.AuthenticateRequest) error {
	// TODO: 实现认证逻辑
	return nil
}

// handleChat 处理聊天
func (h *MessageHandler) handleChat(session *network.Session, msg *protocol.ChatMessage) error {
	// TODO: 实现聊天逻辑
	return nil
}

// handleMove 处理移动
func (h *MessageHandler) handleMove(session *network.Session, msg *protocol.MoveMessage) error {
	// TODO: 实现移动逻辑
	return nil
}

// handleAttack 处理攻击
func (h *MessageHandler) handleAttack(session *network.Session, msg *protocol.AttackMessage) error {
	// TODO: 实现攻击逻辑
	return nil
}

// waitForSignal 等待系统信号
func waitForSignal() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan
}
