#!/bin/bash

# Go传奇服务器数据库快速设置脚本
# 使用方法: ./scripts/setup.sh [mysql_user] [mysql_password]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
MYSQL_USER=${1:-root}
MYSQL_PASSWORD=${2:-123456}
DB_NAME="legend_server"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}=== Go传奇服务器数据库快速设置 ===${NC}"
echo -e "${YELLOW}项目目录: $PROJECT_ROOT${NC}"
echo -e "${YELLOW}MySQL用户: $MYSQL_USER${NC}"
echo -e "${YELLOW}数据库名: $DB_NAME${NC}"
echo ""

# 检查MySQL是否可用
echo -e "${BLUE}🔍 检查MySQL连接...${NC}"
if ! mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${RED}❌ MySQL连接失败！请检查用户名和密码${NC}"
    echo -e "${YELLOW}使用方法: $0 [mysql_user] [mysql_password]${NC}"
    exit 1
fi
echo -e "${GREEN}✅ MySQL连接成功${NC}"

# 检查数据库是否已存在
echo -e "${BLUE}🔍 检查数据库是否存在...${NC}"
if mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $DB_NAME;" > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  数据库 '$DB_NAME' 已存在${NC}"
    read -p "是否要重新创建数据库？这将删除所有现有数据！(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🗑️  删除现有数据库...${NC}"
        mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "DROP DATABASE IF EXISTS $DB_NAME;"
        echo -e "${GREEN}✅ 数据库已删除${NC}"
    else
        echo -e "${YELLOW}❌ 操作已取消${NC}"
        exit 0
    fi
fi

# 执行初始化脚本
echo -e "${BLUE}🚀 开始初始化数据库...${NC}"
if mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" < "$SCRIPT_DIR/init.sql"; then
    echo -e "${GREEN}✅ 数据库初始化完成！${NC}"
else
    echo -e "${RED}❌ 数据库初始化失败！${NC}"
    exit 1
fi

# 验证数据库结构
echo -e "${BLUE}🔍 验证数据库结构...${NC}"
TABLES=$(mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -D"$DB_NAME" -e "SHOW TABLES;" | wc -l)
if [ $TABLES -gt 10 ]; then
    echo -e "${GREEN}✅ 数据库表创建成功 (共 $((TABLES-1)) 个表)${NC}"
else
    echo -e "${RED}❌ 数据库表创建可能有问题${NC}"
fi

# 检查初始数据
echo -e "${BLUE}🔍 检查初始数据...${NC}"
MAPS_COUNT=$(mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -D"$DB_NAME" -e "SELECT COUNT(*) FROM maps;" | tail -n 1)
SKILLS_COUNT=$(mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -D"$DB_NAME" -e "SELECT COUNT(*) FROM skills;" | tail -n 1)
MONSTERS_COUNT=$(mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -D"$DB_NAME" -e "SELECT COUNT(*) FROM monsters;" | tail -n 1)

echo -e "${GREEN}✅ 地图数据: $MAPS_COUNT 个地图${NC}"
echo -e "${GREEN}✅ 技能数据: $SKILLS_COUNT 个技能${NC}"
echo -e "${GREEN}✅ 怪物数据: $MONSTERS_COUNT 个怪物${NC}"

# 更新配置文件
echo -e "${BLUE}🔧 检查配置文件...${NC}"
CONFIG_FILE="$PROJECT_ROOT/configs/gameserver.yaml"
if [ -f "$CONFIG_FILE" ]; then
    # 检查配置文件中的数据库名是否正确
    if grep -q "database: \"$DB_NAME\"" "$CONFIG_FILE"; then
        echo -e "${GREEN}✅ 配置文件数据库名称正确${NC}"
    else
        echo -e "${YELLOW}⚠️  配置文件中的数据库名称可能需要更新${NC}"
        echo -e "${YELLOW}请确保 $CONFIG_FILE 中的数据库名称为: $DB_NAME${NC}"
    fi
    
    # 检查数据库用户名和密码
    if grep -q "username: \"$MYSQL_USER\"" "$CONFIG_FILE"; then
        echo -e "${GREEN}✅ 配置文件用户名正确${NC}"
    else
        echo -e "${YELLOW}⚠️  请检查配置文件中的MySQL用户名${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  配置文件不存在: $CONFIG_FILE${NC}"
fi

# 编译和测试迁移工具
echo -e "${BLUE}🔨 编译迁移工具...${NC}"
cd "$PROJECT_ROOT"
if go build -o migrate cmd/tools/migrate.go 2>/dev/null; then
    echo -e "${GREEN}✅ 迁移工具编译成功${NC}"
    
    # 测试迁移工具
    echo -e "${BLUE}🧪 测试迁移工具...${NC}"
    if ./migrate -config configs/gameserver.yaml -action migrate 2>/dev/null; then
        echo -e "${GREEN}✅ 迁移工具测试成功${NC}"
    else
        echo -e "${YELLOW}⚠️  迁移工具测试失败，可能需要检查配置${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  迁移工具编译失败，请检查Go环境${NC}"
fi

# 显示下一步操作
echo ""
echo -e "${BLUE}=== 设置完成 ===${NC}"
echo -e "${GREEN}✅ 数据库初始化成功！${NC}"
echo ""
echo -e "${YELLOW}下一步操作:${NC}"
echo -e "1. 检查配置文件: ${BLUE}configs/gameserver.yaml${NC}"
echo -e "2. 启动游戏服务器: ${BLUE}go run cmd/gameserver/main.go${NC}"
echo -e "3. 插入测试数据: ${BLUE}./migrate -action seed${NC}"
echo ""
echo -e "${YELLOW}数据库管理命令:${NC}"
echo -e "- 查看数据库: ${BLUE}mysql -u$MYSQL_USER -p$MYSQL_PASSWORD -D$DB_NAME${NC}"
echo -e "- 重置数据库: ${BLUE}./migrate -action reset${NC}"
echo -e "- 插入测试数据: ${BLUE}./migrate -action seed${NC}"
echo ""
echo -e "${GREEN}🎉 祝你游戏开发愉快！${NC}"
