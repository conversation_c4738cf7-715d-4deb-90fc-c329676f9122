# Go传奇游戏服务端

基于原C++传奇服务端的Go语言重构版本，采用现代化的微服务架构和高并发设计。

## 项目结构

```
go-legend-server/
├── cmd/                    # 应用程序入口
│   ├── gameserver/        # 游戏服务器
│   ├── gateway/           # 网关服务器
│   └── tools/             # 工具程序
├── internal/              # 内部包（不对外暴露）
│   ├── config/           # 配置管理
│   ├── database/         # 数据库层
│   ├── game/             # 游戏逻辑
│   ├── network/          # 网络层
│   ├── protocol/         # 协议定义
│   └── utils/            # 工具函数
├── pkg/                   # 公共包（可对外暴露）
│   ├── logger/           # 日志系统
│   ├── cache/            # 缓存系统
│   └── metrics/          # 监控指标
├── api/                   # API定义
│   ├── proto/            # protobuf定义
│   └── http/             # HTTP API
├── configs/               # 配置文件
├── scripts/               # 脚本文件
├── docs/                  # 文档
├── deployments/           # 部署配置
├── test/                  # 测试文件
├── go.mod
├── go.sum
├── Makefile
└── README.md
```

## 核心特性

### 1. 高性能网络架构
- 基于Go原生goroutine的并发模型
- TCP长连接管理
- 消息队列和异步处理
- 连接池和对象池优化

### 2. 模块化游戏逻辑
- 玩家系统（角色、属性、技能）
- 地图系统（场景、移动、碰撞检测）
- 战斗系统（攻击、技能、伤害计算）
- 社交系统（公会、组队、好友）
- 经济系统（物品、交易、寄售）

### 3. 数据持久化
- MySQL主数据库
- Redis缓存和会话存储
- 数据库连接池
- 自动化数据迁移

### 4. 可观测性
- 结构化日志
- 性能监控
- 链路追踪
- 健康检查

### 5. 部署和运维
- Docker容器化
- Kubernetes部署
- 配置热更新
- 优雅关闭

## 技术栈

- **语言**: Go 1.21+
- **数据库**: MySQL 8.0, Redis 7.0
- **消息队列**: NATS/RabbitMQ
- **监控**: Prometheus + Grafana
- **日志**: Zap + ELK Stack
- **部署**: Docker + Kubernetes

## 快速开始

### 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis 7.0+

### 安装依赖
```bash
go mod download
```

### 配置数据库
```bash
# 创建数据库
mysql -u root -p < scripts/init.sql

# 运行迁移
go run cmd/tools/migrate.go
```

### 启动服务
```bash
# 启动游戏服务器
go run cmd/gameserver/main.go

# 启动网关服务器
go run cmd/gateway/main.go
```

## 开发指南

### 代码规范
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 通过golint和go vet检查
- 单元测试覆盖率 > 80%

### 提交规范
- 使用Conventional Commits规范
- 每个PR必须通过CI检查
- 代码审查通过后才能合并

## 性能目标

- 单服支持10,000+并发连接
- 消息处理延迟 < 10ms
- 数据库查询响应时间 < 50ms
- 内存使用稳定，无内存泄漏

## 许可证

MIT License
