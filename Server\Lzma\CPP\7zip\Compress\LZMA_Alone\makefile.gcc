PROG = lzma
CXX = g++ -O2 -Wall
CXX_C = gcc -O2 -Wall
LIB = -lm
RM = rm -f
CFLAGS = -c

ifdef SystemDrive
IS_MINGW = 1
endif

ifdef IS_MINGW
FILE_IO =FileIO
FILE_IO_2 =Windows/$(FILE_IO)
LIB2 = -luuid
else
FILE_IO =C_FileIO
FILE_IO_2 =Common/$(FILE_IO)
endif

OBJS = \
  LzmaAlone.o \
  LzmaBench.o \
  LzmaBenchCon.o \
  LzmaDecoder.o \
  LzmaEncoder.o \
  InBuffer.o \
  OutBuffer.o \
  FileStreams.o \
  StreamUtils.o \
  $(FILE_IO).o \
  CommandLineParser.o \
  CRC.o \
  IntToString.o \
  MyString.o \
  StringConvert.o \
  StringToInt.o \
  MyVector.o \
  7zCrc.o \
  Alloc.o \
  Bra86.o \
  LzFind.o \
  LzmaDec.o \
  LzmaEnc.o \
  Lzma86Dec.o \
  Lzma86Enc.o \


all: $(PROG)

$(PROG): $(OBJS)
	$(CXX) -o $(PROG) $(LDFLAGS) $(OBJS) $(LIB) $(LIB2)

LzmaAlone.o: LzmaAlone.cpp
	$(CXX) $(CFLAGS) LzmaAlone.cpp

LzmaBench.o: LzmaBench.cpp
	$(CXX) $(CFLAGS) LzmaBench.cpp

LzmaBenchCon.o: LzmaBenchCon.cpp
	$(CXX) $(CFLAGS) LzmaBenchCon.cpp

LzmaDecoder.o: ../LzmaDecoder.cpp
	$(CXX) $(CFLAGS) ../LzmaDecoder.cpp

LzmaEncoder.o: ../LzmaEncoder.cpp
	$(CXX) $(CFLAGS) ../LzmaEncoder.cpp

InBuffer.o: ../../Common/InBuffer.cpp
	$(CXX) $(CFLAGS) ../../Common/InBuffer.cpp

OutBuffer.o: ../../Common/OutBuffer.cpp
	$(CXX) $(CFLAGS) ../../Common/OutBuffer.cpp

FileStreams.o: ../../Common/FileStreams.cpp
	$(CXX) $(CFLAGS) ../../Common/FileStreams.cpp

StreamUtils.o: ../../Common/StreamUtils.cpp
	$(CXX) $(CFLAGS) ../../Common/StreamUtils.cpp

$(FILE_IO).o: ../../../$(FILE_IO_2).cpp
	$(CXX) $(CFLAGS) ../../../$(FILE_IO_2).cpp


CommandLineParser.o: ../../../Common/CommandLineParser.cpp
	$(CXX) $(CFLAGS) ../../../Common/CommandLineParser.cpp

CRC.o: ../../../Common/CRC.cpp
	$(CXX) $(CFLAGS) ../../../Common/CRC.cpp

MyWindows.o: ../../../Common/MyWindows.cpp
	$(CXX) $(CFLAGS) ../../../Common/MyWindows.cpp

IntToString.o: ../../../Common/IntToString.cpp
	$(CXX) $(CFLAGS) ../../../Common/IntToString.cpp

MyString.o: ../../../Common/MyString.cpp
	$(CXX) $(CFLAGS) ../../../Common/MyString.cpp

StringConvert.o: ../../../Common/StringConvert.cpp
	$(CXX) $(CFLAGS) ../../../Common/StringConvert.cpp

StringToInt.o: ../../../Common/StringToInt.cpp
	$(CXX) $(CFLAGS) ../../../Common/StringToInt.cpp

MyVector.o: ../../../Common/MyVector.cpp
	$(CXX) $(CFLAGS) ../../../Common/MyVector.cpp

7zCrc.o: ../../../../C/7zCrc.c
	$(CXX_C) $(CFLAGS) ../../../../C/7zCrc.c

Alloc.o: ../../../../C/Alloc.c
	$(CXX_C) $(CFLAGS) ../../../../C/Alloc.c

Bra86.o: ../../../../C/Bra86.c
	$(CXX_C) $(CFLAGS) ../../../../C/Bra86.c

LzFind.o: ../../../../C/LzFind.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzFind.c

LzmaDec.o: ../../../../C/LzmaDec.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzmaDec.c

LzmaEnc.o: ../../../../C/LzmaEnc.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzmaEnc.c

Lzma86Dec.o: ../../../../C/LzmaUtil/Lzma86Dec.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzmaUtil/Lzma86Dec.c

Lzma86Enc.o: ../../../../C/LzmaUtil/Lzma86Enc.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzmaUtil/Lzma86Enc.c

clean:
	-$(RM) $(PROG) $(OBJS)

