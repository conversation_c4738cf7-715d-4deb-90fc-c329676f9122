package logger

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/legend-server/go-legend-server/internal/config"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var globalLogger *zap.Logger

// Init 初始化日志系统
func Init(cfg *config.LogConfig) error {
	// 解析日志级别
	level, err := zapcore.ParseLevel(cfg.Level)
	if err != nil {
		return fmt.Errorf("invalid log level: %w", err)
	}

	// 创建编码器配置
	var encoderConfig zapcore.EncoderConfig
	if cfg.Format == "json" {
		encoderConfig = zap.NewProductionEncoderConfig()
	} else {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	}

	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	// 创建编码器
	var encoder zapcore.Encoder
	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 创建写入器
	var writers []zapcore.WriteSyncer

	// 控制台输出
	if cfg.Output == "console" || cfg.Output == "both" {
		writers = append(writers, zapcore.AddSync(os.Stdout))
	}

	// 文件输出
	if cfg.Output == "file" || cfg.Output == "both" {
		// 确保日志目录存在
		if err := os.MkdirAll(filepath.Dir(cfg.Filename), 0755); err != nil {
			return fmt.Errorf("failed to create log directory: %w", err)
		}

		// 配置日志轮转
		fileWriter := &lumberjack.Logger{
			Filename:   cfg.Filename,
			MaxSize:    cfg.MaxSize,
			MaxAge:     cfg.MaxAge,
			MaxBackups: cfg.MaxBackups,
			Compress:   cfg.Compress,
		}

		writers = append(writers, zapcore.AddSync(fileWriter))
	}

	if len(writers) == 0 {
		return fmt.Errorf("no log output configured")
	}

	// 合并写入器
	writer := zapcore.NewMultiWriteSyncer(writers...)

	// 创建核心
	core := zapcore.NewCore(encoder, writer, level)

	// 创建日志器
	globalLogger = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return nil
}

// GetLogger 获取全局日志器
func GetLogger() *zap.Logger {
	if globalLogger == nil {
		// 如果没有初始化，使用默认配置
		config := zap.NewDevelopmentConfig()
		logger, _ := config.Build()
		return logger
	}
	return globalLogger
}

// Sync 同步日志缓冲区
func Sync() {
	if globalLogger != nil {
		globalLogger.Sync()
	}
}

// Debug 调试日志
func Debug(msg string, fields ...zap.Field) {
	GetLogger().Debug(msg, fields...)
}

// Info 信息日志
func Info(msg string, fields ...zap.Field) {
	GetLogger().Info(msg, fields...)
}

// Warn 警告日志
func Warn(msg string, fields ...zap.Field) {
	GetLogger().Warn(msg, fields...)
}

// Error 错误日志
func Error(msg string, fields ...zap.Field) {
	GetLogger().Error(msg, fields...)
}

// Fatal 致命错误日志
func Fatal(msg string, fields ...zap.Field) {
	GetLogger().Fatal(msg, fields...)
}

// Panic panic日志
func Panic(msg string, fields ...zap.Field) {
	GetLogger().Panic(msg, fields...)
}

// With 创建带有字段的子日志器
func With(fields ...zap.Field) *zap.Logger {
	return GetLogger().With(fields...)
}

// Named 创建命名的子日志器
func Named(name string) *zap.Logger {
	return GetLogger().Named(name)
}
