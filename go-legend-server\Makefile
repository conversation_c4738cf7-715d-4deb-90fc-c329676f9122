# Go传奇游戏服务端 Makefile

# 变量定义
APP_NAME = go-legend-server
VERSION = 1.0.0
BUILD_TIME = $(shell date +%Y-%m-%d\ %H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD)
GO_VERSION = $(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS = -ldflags "-X main.version=$(VERSION) -X 'main.buildTime=$(BUILD_TIME)' -X main.gitCommit=$(GIT_COMMIT)"

# 目录
BIN_DIR = bin
CMD_DIR = cmd
CONFIGS_DIR = configs
SCRIPTS_DIR = scripts

# 默认目标
.PHONY: all
all: clean build

# 构建所有二进制文件
.PHONY: build
build: build-gameserver build-gateway build-tools

# 构建游戏服务器
.PHONY: build-gameserver
build-gameserver:
	@echo "Building gameserver..."
	@mkdir -p $(BIN_DIR)
	go build $(LDFLAGS) -o $(BIN_DIR)/gameserver $(CMD_DIR)/gameserver/main.go

# 构建网关服务器
.PHONY: build-gateway
build-gateway:
	@echo "Building gateway..."
	@mkdir -p $(BIN_DIR)
	go build $(LDFLAGS) -o $(BIN_DIR)/gateway $(CMD_DIR)/gateway/main.go

# 构建工具
.PHONY: build-tools
build-tools:
	@echo "Building tools..."
	@mkdir -p $(BIN_DIR)
	go build $(LDFLAGS) -o $(BIN_DIR)/migrate $(CMD_DIR)/tools/migrate.go

# 交叉编译
.PHONY: build-linux
build-linux:
	@echo "Building for Linux..."
	@mkdir -p $(BIN_DIR)/linux
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BIN_DIR)/linux/gameserver $(CMD_DIR)/gameserver/main.go
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BIN_DIR)/linux/gateway $(CMD_DIR)/gateway/main.go

.PHONY: build-windows
build-windows:
	@echo "Building for Windows..."
	@mkdir -p $(BIN_DIR)/windows
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(BIN_DIR)/windows/gameserver.exe $(CMD_DIR)/gameserver/main.go
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(BIN_DIR)/windows/gateway.exe $(CMD_DIR)/gateway/main.go

# 运行
.PHONY: run-gameserver
run-gameserver: build-gameserver
	@echo "Running gameserver..."
	./$(BIN_DIR)/gameserver -config $(CONFIGS_DIR)/gameserver.yaml

.PHONY: run-gateway
run-gateway: build-gateway
	@echo "Running gateway..."
	./$(BIN_DIR)/gateway -config $(CONFIGS_DIR)/gateway.yaml

# 测试
.PHONY: test
test:
	@echo "Running tests..."
	go test -v -race -coverprofile=coverage.out ./...

.PHONY: test-coverage
test-coverage: test
	@echo "Generating coverage report..."
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 基准测试
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	go test -bench=. -benchmem ./...

# 代码质量检查
.PHONY: lint
lint:
	@echo "Running linter..."
	golangci-lint run

.PHONY: fmt
fmt:
	@echo "Formatting code..."
	go fmt ./...

.PHONY: vet
vet:
	@echo "Running go vet..."
	go vet ./...

.PHONY: check
check: fmt vet lint test

# 依赖管理
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	go mod download

.PHONY: deps-update
deps-update:
	@echo "Updating dependencies..."
	go get -u ./...
	go mod tidy

.PHONY: deps-verify
deps-verify:
	@echo "Verifying dependencies..."
	go mod verify

# 清理
.PHONY: clean
clean:
	@echo "Cleaning..."
	rm -rf $(BIN_DIR)
	rm -f coverage.out coverage.html

# Docker
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -d --name $(APP_NAME) -p 7863:7863 $(APP_NAME):latest

.PHONY: docker-stop
docker-stop:
	@echo "Stopping Docker container..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

# 数据库
.PHONY: db-migrate
db-migrate: build-tools
	@echo "Running database migration..."
	./$(BIN_DIR)/migrate -config $(CONFIGS_DIR)/gameserver.yaml

.PHONY: db-seed
db-seed:
	@echo "Seeding database..."
	mysql -u root -p < $(SCRIPTS_DIR)/seed.sql

# 部署
.PHONY: deploy-dev
deploy-dev: build
	@echo "Deploying to development environment..."
	# 添加部署脚本

.PHONY: deploy-prod
deploy-prod: build-linux
	@echo "Deploying to production environment..."
	# 添加生产部署脚本

# 监控和日志
.PHONY: logs
logs:
	@echo "Showing logs..."
	tail -f logs/gameserver.log

.PHONY: monitor
monitor:
	@echo "Starting monitoring..."
	# 启动监控工具

# 帮助
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build          - Build all binaries"
	@echo "  build-gameserver - Build game server"
	@echo "  build-gateway  - Build gateway server"
	@echo "  build-tools    - Build tools"
	@echo "  build-linux    - Cross compile for Linux"
	@echo "  build-windows  - Cross compile for Windows"
	@echo "  run-gameserver - Run game server"
	@echo "  run-gateway    - Run gateway server"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  bench          - Run benchmarks"
	@echo "  lint           - Run linter"
	@echo "  fmt            - Format code"
	@echo "  vet            - Run go vet"
	@echo "  check          - Run all checks"
	@echo "  deps           - Download dependencies"
	@echo "  deps-update    - Update dependencies"
	@echo "  deps-verify    - Verify dependencies"
	@echo "  clean          - Clean build artifacts"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  docker-stop    - Stop Docker container"
	@echo "  db-migrate     - Run database migration"
	@echo "  db-seed        - Seed database"
	@echo "  deploy-dev     - Deploy to development"
	@echo "  deploy-prod    - Deploy to production"
	@echo "  logs           - Show logs"
	@echo "  monitor        - Start monitoring"
	@echo "  help           - Show this help"

# 版本信息
.PHONY: version
version:
	@echo "App Name: $(APP_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Git Commit: $(GIT_COMMIT)"
	@echo "Go Version: $(GO_VERSION)"
