package game

import (
	"fmt"
	"sync"

	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// EquipmentManager 装备管理器
type EquipmentManager struct {
	player    *Player
	equipment map[EquipmentPosition]*Equipment // 已装备的装备
	mutex     sync.RWMutex
}

// NewEquipmentManager 创建装备管理器
func NewEquipmentManager(player *Player) *EquipmentManager {
	return &EquipmentManager{
		player:    player,
		equipment: make(map[EquipmentPosition]*Equipment),
	}
}

// EquipItem 装备物品
func (em *EquipmentManager) EquipItem(equipment *Equipment) error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	// 检查是否可以装备
	if err := equipment.CanEquip(em.player); err != nil {
		return fmt.Errorf("cannot equip item: %w", err)
	}

	position := equipment.GetPosition()

	// 对于可以装备在多个位置的装备（如戒指、手镯），选择空闲位置
	if equipment.Type == EquipTypeRing {
		if _, exists := em.equipment[EquipPosRing1]; !exists {
			position = EquipPosRing1
		} else if _, exists := em.equipment[EquipPosRing2]; !exists {
			position = EquipPosRing2
		} else {
			return fmt.Errorf("no available ring slot")
		}
	} else if equipment.Type == EquipTypeBracelet {
		if _, exists := em.equipment[EquipPosBracelet1]; !exists {
			position = EquipPosBracelet1
		} else if _, exists := em.equipment[EquipPosBracelet2]; !exists {
			position = EquipPosBracelet2
		} else {
			return fmt.Errorf("no available bracelet slot")
		}
	}

	// 如果位置已有装备，先卸下
	if oldEquipment, exists := em.equipment[position]; exists {
		if err := em.unequipInternal(position); err != nil {
			return fmt.Errorf("failed to unequip old item: %w", err)
		}

		// 将卸下的装备放回背包
		if !em.player.inventory.AddItemToEmptySlot(em.equipmentToItem(oldEquipment)) {
			// 如果背包满了，装备失败
			em.equipment[position] = oldEquipment
			em.applyEquipmentAttributes(oldEquipment, true)
			return fmt.Errorf("inventory is full")
		}
	}

	// 装备新装备
	em.equipment[position] = equipment

	// 应用装备属性
	em.applyEquipmentAttributes(equipment, true)

	// 绑定装备（如果需要）
	if !equipment.IsBound {
		equipment.Bind(uint64(em.player.ID))
	}

	logger.Info("Player equipped item",
		zap.String("player", em.player.CharName),
		zap.String("equipment", equipment.Name),
		zap.Int32("position", int32(position)))

	return nil
}

// UnequipItem 卸下装备
func (em *EquipmentManager) UnequipItem(position EquipmentPosition) (*Equipment, error) {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	equipment, exists := em.equipment[position]
	if !exists {
		return nil, fmt.Errorf("no equipment at position %d", position)
	}

	// 检查背包是否有空位
	if !em.player.inventory.HasEmptySlot() {
		return nil, fmt.Errorf("inventory is full")
	}

	// 卸下装备
	if err := em.unequipInternal(position); err != nil {
		return nil, err
	}

	// 将装备放回背包
	if !em.player.inventory.AddItemToEmptySlot(em.equipmentToItem(equipment)) {
		// 如果放回背包失败，重新装备
		em.equipment[position] = equipment
		em.applyEquipmentAttributes(equipment, true)
		return nil, fmt.Errorf("failed to add item to inventory")
	}

	logger.Info("Player unequipped item",
		zap.String("player", em.player.CharName),
		zap.String("equipment", equipment.Name),
		zap.Int32("position", int32(position)))

	return equipment, nil
}

// unequipInternal 内部卸装备方法
func (em *EquipmentManager) unequipInternal(position EquipmentPosition) error {
	equipment, exists := em.equipment[position]
	if !exists {
		return fmt.Errorf("no equipment at position %d", position)
	}

	// 移除装备属性
	em.applyEquipmentAttributes(equipment, false)

	// 从装备位置移除
	delete(em.equipment, position)

	return nil
}

// GetEquippedItem 获取指定位置的装备
func (em *EquipmentManager) GetEquippedItem(position EquipmentPosition) (*Equipment, bool) {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	equipment, exists := em.equipment[position]
	return equipment, exists
}

// GetAllEquippedItems 获取所有已装备的物品
func (em *EquipmentManager) GetAllEquippedItems() map[EquipmentPosition]*Equipment {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	result := make(map[EquipmentPosition]*Equipment)
	for pos, equipment := range em.equipment {
		result[pos] = equipment
	}
	return result
}

// CalculateTotalAttributes 计算所有装备的总属性
func (em *EquipmentManager) CalculateTotalAttributes() EquipmentAttributes {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	var total EquipmentAttributes

	for _, equipment := range em.equipment {
		if equipment != nil && !equipment.IsBroken() {
			attrs := equipment.GetTotalAttributes()
			total.Attack += attrs.Attack
			total.Defense += attrs.Defense
			total.MagicAttack += attrs.MagicAttack
			total.MagicDefense += attrs.MagicDefense
			total.Accuracy += attrs.Accuracy
			total.Agility += attrs.Agility
			total.HPBonus += attrs.HPBonus
			total.MPBonus += attrs.MPBonus
			total.CriticalRate += attrs.CriticalRate
			total.CriticalDamage += attrs.CriticalDamage
			total.AttackSpeed += attrs.AttackSpeed
			total.MoveSpeed += attrs.MoveSpeed
			total.LifeSteal += attrs.LifeSteal
			total.ManaSteal += attrs.ManaSteal
			total.FireResistance += attrs.FireResistance
			total.IceResistance += attrs.IceResistance
			total.LightResistance += attrs.LightResistance
			total.PoisonResistance += attrs.PoisonResistance
		}
	}

	return total
}

// applyEquipmentAttributes 应用或移除装备属性
func (em *EquipmentManager) applyEquipmentAttributes(equipment *Equipment, apply bool) {
	if equipment == nil || equipment.IsBroken() {
		return
	}

	attrs := equipment.GetTotalAttributes()
	multiplier := int32(1)
	if !apply {
		multiplier = -1
	}

	// 应用基础属性
	em.player.Attack += attrs.Attack * multiplier
	em.player.Defense += attrs.Defense * multiplier
	em.player.MagicAttack += attrs.MagicAttack * multiplier
	em.player.MagicDefense += attrs.MagicDefense * multiplier
	em.player.Accuracy += attrs.Accuracy * multiplier
	em.player.Agility += attrs.Agility * multiplier

	// 应用生命魔法加成
	em.player.MaxHP += attrs.HPBonus * multiplier
	em.player.MaxMP += attrs.MPBonus * multiplier

	// 确保当前HP/MP不超过最大值
	if em.player.HP > em.player.MaxHP {
		em.player.HP = em.player.MaxHP
	}
	if em.player.MP > em.player.MaxMP {
		em.player.MP = em.player.MaxMP
	}
}

// DamageEquipment 装备受损
func (em *EquipmentManager) DamageEquipment(damageAmount int32) {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	// 随机选择装备受损
	for _, equipment := range em.equipment {
		if equipment != nil && !equipment.IsBroken() {
			equipment.TakeDamage(damageAmount)

			// 如果装备损坏，移除其属性加成
			if equipment.IsBroken() {
				em.applyEquipmentAttributes(equipment, false)
				logger.Warn("Equipment broken",
					zap.String("player", em.player.CharName),
					zap.String("equipment", equipment.Name))
			}
			break // 只损坏一件装备
		}
	}
}

// RepairAllEquipment 修理所有装备
func (em *EquipmentManager) RepairAllEquipment() {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	for _, equipment := range em.equipment {
		if equipment != nil && equipment.IsBroken() {
			equipment.Repair()
			// 重新应用属性
			em.applyEquipmentAttributes(equipment, true)
		}
	}
}

// equipmentToItem 将装备转换为物品
func (em *EquipmentManager) equipmentToItem(equipment *Equipment) *Item {
	return &Item{
		ID:            equipment.ID,
		ItemID:        equipment.ItemID,
		Count:         1,
		Durability:    equipment.Durability,
		MaxDurability: equipment.MaxDurability,
		Properties:    make(map[string]int32),
	}
}

// HasEmptySlot 检查背包是否有空位（需要在Inventory中实现）
func (inv *Inventory) HasEmptySlot() bool {
	inv.mutex.RLock()
	defer inv.mutex.RUnlock()

	// 假设背包有40个位置
	for i := int32(0); i < 40; i++ {
		if _, exists := inv.items[i]; !exists {
			return true
		}
	}
	return false
}

// AddItemToEmptySlot 添加物品到空位（需要在Inventory中实现）
func (inv *Inventory) AddItemToEmptySlot(item *Item) bool {
	inv.mutex.Lock()
	defer inv.mutex.Unlock()

	// 找到第一个空位
	for i := int32(0); i < 40; i++ {
		if _, exists := inv.items[i]; !exists {
			inv.items[i] = item
			return true
		}
	}
	return false
}
