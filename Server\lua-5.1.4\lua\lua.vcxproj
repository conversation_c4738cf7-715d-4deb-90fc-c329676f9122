﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="111|Win32">
      <Configuration>111</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseL|Win32">
      <Configuration>ReleaseL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{79C9F726-3AA3-4FA5-87DF-5AB8963DD97D}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseL|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='111|Win32'">
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Debug\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Release\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='ReleaseL|Win32'">$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='ReleaseL|Win32'">$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <BrowseInformation>true</BrowseInformation>
    </ClCompile>
    <Lib>
      <OutputFile>$(OutDir)lua.lib</OutputFile>
    </Lib>
    <Bscmake>
      <PreserveSbr>true</PreserveSbr>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;NODUMP;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Lib>
      <OutputFile>$(OutDir)lua.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseL|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;NODUMP;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <BrowseInformation>true</BrowseInformation>
    </ClCompile>
    <Lib>
      <OutputFile>$(OutDir)lua.lib</OutputFile>
    </Lib>
    <Bscmake>
      <PreserveSbr>true</PreserveSbr>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\src\lapi.c" />
    <ClCompile Include="..\src\lcode.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\ldebug.c" />
    <ClCompile Include="..\src\ldo.c" />
    <ClCompile Include="..\src\ldump.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\lfunc.c" />
    <ClCompile Include="..\src\lgc.c" />
    <ClCompile Include="..\src\llex.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\lmem.c" />
    <ClCompile Include="..\src\lobject.c" />
    <ClCompile Include="..\src\lopcodes.c" />
    <ClCompile Include="..\src\lparser.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\lstate.c" />
    <ClCompile Include="..\src\lstring.c" />
    <ClCompile Include="..\src\ltable.c" />
    <ClCompile Include="..\src\ltm.c" />
    <ClCompile Include="..\src\lundump.c" />
    <ClCompile Include="..\src\lvm.c" />
    <ClCompile Include="..\src\lzio.c" />
    <ClCompile Include="..\src\noparser.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='ReleaseL|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\lauxlib.c" />
    <ClCompile Include="..\src\lbaselib.c" />
    <ClCompile Include="..\src\ldblib.c" />
    <ClCompile Include="..\src\linit.c" />
    <ClCompile Include="..\src\liolib.c" />
    <ClCompile Include="..\src\lmathlib.c" />
    <ClCompile Include="..\src\loadlib.c" />
    <ClCompile Include="..\src\loslib.c" />
    <ClCompile Include="..\src\lstrlib.c" />
    <ClCompile Include="..\src\ltablib.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\src\lapi.h" />
    <ClInclude Include="..\src\lauxlib.h" />
    <ClInclude Include="..\src\lcode.h" />
    <ClInclude Include="..\src\ldebug.h" />
    <ClInclude Include="..\src\ldo.h" />
    <ClInclude Include="..\src\lfunc.h" />
    <ClInclude Include="..\src\lgc.h" />
    <ClInclude Include="..\src\llex.h" />
    <ClInclude Include="..\src\llimits.h" />
    <ClInclude Include="..\src\lmem.h" />
    <ClInclude Include="..\src\lobject.h" />
    <ClInclude Include="..\src\lopcodes.h" />
    <ClInclude Include="..\src\lparser.h" />
    <ClInclude Include="..\src\lstate.h" />
    <ClInclude Include="..\src\lstring.h" />
    <ClInclude Include="..\src\ltable.h" />
    <ClInclude Include="..\src\ltm.h" />
    <ClInclude Include="..\src\lua.h" />
    <ClInclude Include="..\src\luaconf.h" />
    <ClInclude Include="..\src\lualib.h" />
    <ClInclude Include="..\src\lundump.h" />
    <ClInclude Include="..\src\lvm.h" />
    <ClInclude Include="..\src\lzio.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>