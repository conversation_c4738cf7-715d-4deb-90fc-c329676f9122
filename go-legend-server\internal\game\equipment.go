package game

import (
	"fmt"
	"sync"
	"time"
)

// EquipmentType 装备类型
type EquipmentType int32

const (
	EquipTypeWeapon   EquipmentType = iota // 武器
	EquipTypeHelmet                        // 头盔
	EquipTypeArmor                         // 盔甲
	EquipTypeNecklace                      // 项链
	EquipTypeBracelet                      // 手镯
	EquipTypeRing                          // 戒指
	EquipTypeBelt                          // 腰带
	EquipTypeBoots                         // 靴子
	EquipTypeGem                           // 宝石
	EquipTypeBook                          // 技能书
	EquipTypeCharm                         // 护身符
)

// EquipmentPosition 装备位置
type EquipmentPosition int32

const (
	EquipPosWeapon    EquipmentPosition = 0  // 武器位置
	EquipPosHelmet    EquipmentPosition = 1  // 头盔位置
	EquipPosArmor     EquipmentPosition = 2  // 盔甲位置
	EquipPosNecklace  EquipmentPosition = 3  // 项链位置
	EquipPosBracelet1 EquipmentPosition = 4  // 左手镯
	EquipPosBracelet2 EquipmentPosition = 5  // 右手镯
	EquipPosRing1     EquipmentPosition = 6  // 左戒指
	EquipPosRing2     EquipmentPosition = 7  // 右戒指
	EquipPosBelt      EquipmentPosition = 8  // 腰带位置
	EquipPosBoots     EquipmentPosition = 9  // 靴子位置
	EquipPosGem       EquipmentPosition = 10 // 宝石位置
	EquipPosCharm     EquipmentPosition = 11 // 护身符位置
)

// EquipmentQuality 装备品质
type EquipmentQuality int32

const (
	QualityCommon    EquipmentQuality = iota // 普通
	QualityUncommon                          // 优秀
	QualityRare                              // 稀有
	QualityEpic                              // 史诗
	QualityLegendary                         // 传说
	QualityArtifact                          // 神器
)

// Equipment 装备
type Equipment struct {
	// 基础信息
	ID      int32            `json:"id"`
	ItemID  int32            `json:"item_id"`
	Name    string           `json:"name"`
	Type    EquipmentType    `json:"type"`
	Quality EquipmentQuality `json:"quality"`
	Level   int32            `json:"level"`

	// 使用限制
	RequiredLevel  int32      `json:"required_level"`
	RequiredJob    JobType    `json:"required_job"`
	RequiredGender GenderType `json:"required_gender"`

	// 耐久度
	Durability    int32 `json:"durability"`
	MaxDurability int32 `json:"max_durability"`

	// 基础属性
	Attack       int32 `json:"attack"`
	Defense      int32 `json:"defense"`
	MagicAttack  int32 `json:"magic_attack"`
	MagicDefense int32 `json:"magic_defense"`
	Accuracy     int32 `json:"accuracy"`
	Agility      int32 `json:"agility"`

	// 生命魔法加成
	HPBonus int32 `json:"hp_bonus"`
	MPBonus int32 `json:"mp_bonus"`

	// 特殊属性
	CriticalRate   int32 `json:"critical_rate"`   // 暴击率
	CriticalDamage int32 `json:"critical_damage"` // 暴击伤害
	AttackSpeed    int32 `json:"attack_speed"`    // 攻击速度
	MoveSpeed      int32 `json:"move_speed"`      // 移动速度
	LifeSteal      int32 `json:"life_steal"`      // 生命偷取
	ManaSteal      int32 `json:"mana_steal"`      // 魔法偷取

	// 抗性
	FireResistance   int32 `json:"fire_resistance"`   // 火抗
	IceResistance    int32 `json:"ice_resistance"`    // 冰抗
	LightResistance  int32 `json:"light_resistance"`  // 雷抗
	PoisonResistance int32 `json:"poison_resistance"` // 毒抗

	// 强化信息
	EnhanceLevel int32          `json:"enhance_level"` // 强化等级
	Gems         map[int32]*Gem `json:"gems"`          // 镶嵌的宝石

	// 绑定信息
	IsBound  bool      `json:"is_bound"`
	BindTime time.Time `json:"bind_time"`
	OwnerID  uint64    `json:"owner_id"`

	// 时效信息
	ExpireTime *time.Time `json:"expire_time,omitempty"`

	// 运行时数据
	mutex sync.RWMutex `json:"-"`
}

// Gem 宝石
type Gem struct {
	ID         int32            `json:"id"`
	Type       GemType          `json:"type"`
	Level      int32            `json:"level"`
	Attributes map[string]int32 `json:"attributes"`
}

// GemType 宝石类型
type GemType int32

const (
	GemTypeAttack  GemType = iota // 攻击宝石
	GemTypeDefense                // 防御宝石
	GemTypeMagic                  // 魔法宝石
	GemTypeLife                   // 生命宝石
	GemTypeSpecial                // 特殊宝石
)

// NewEquipment 创建装备
func NewEquipment(itemID int32, name string, equipType EquipmentType, quality EquipmentQuality) *Equipment {
	return &Equipment{
		ItemID:        itemID,
		Name:          name,
		Type:          equipType,
		Quality:       quality,
		Level:         1,
		MaxDurability: 100,
		Durability:    100,
		Gems:          make(map[int32]*Gem),
		IsBound:       false,
	}
}

// CanEquip 检查是否可以装备
func (e *Equipment) CanEquip(player *Player) error {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	// 检查等级要求
	if player.Level < e.RequiredLevel {
		return fmt.Errorf("level requirement not met: required %d, current %d", e.RequiredLevel, player.Level)
	}

	// 检查职业要求
	if e.RequiredJob != JobAll && e.RequiredJob != player.Job {
		return fmt.Errorf("job requirement not met: required %d, current %d", e.RequiredJob, player.Job)
	}

	// 检查性别要求
	if e.RequiredGender != GenderAll && e.RequiredGender != player.Gender {
		return fmt.Errorf("gender requirement not met")
	}

	// 检查耐久度
	if e.Durability <= 0 {
		return fmt.Errorf("equipment is broken")
	}

	// 检查是否过期
	if e.ExpireTime != nil && time.Now().After(*e.ExpireTime) {
		return fmt.Errorf("equipment has expired")
	}

	return nil
}

// GetPosition 获取装备应该装备的位置
func (e *Equipment) GetPosition() EquipmentPosition {
	switch e.Type {
	case EquipTypeWeapon:
		return EquipPosWeapon
	case EquipTypeHelmet:
		return EquipPosHelmet
	case EquipTypeArmor:
		return EquipPosArmor
	case EquipTypeNecklace:
		return EquipPosNecklace
	case EquipTypeBracelet:
		return EquipPosBracelet1 // 默认左手镯，具体位置由装备系统决定
	case EquipTypeRing:
		return EquipPosRing1 // 默认左戒指，具体位置由装备系统决定
	case EquipTypeBelt:
		return EquipPosBelt
	case EquipTypeBoots:
		return EquipPosBoots
	case EquipTypeGem:
		return EquipPosGem
	case EquipTypeCharm:
		return EquipPosCharm
	default:
		return EquipPosWeapon
	}
}

// Repair 修理装备
func (e *Equipment) Repair() {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	e.Durability = e.MaxDurability
}

// TakeDamage 装备受损
func (e *Equipment) TakeDamage(damage int32) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	e.Durability -= damage
	if e.Durability < 0 {
		e.Durability = 0
	}
}

// IsBroken 是否损坏
func (e *Equipment) IsBroken() bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	return e.Durability <= 0
}

// Bind 绑定装备
func (e *Equipment) Bind(playerID uint64) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	e.IsBound = true
	e.BindTime = time.Now()
	e.OwnerID = playerID
}

// Enhance 强化装备
func (e *Equipment) Enhance() bool {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	// 简单的强化逻辑，实际游戏中会更复杂
	if e.EnhanceLevel >= 15 {
		return false // 最高强化等级
	}

	// 强化成功率随等级递减
	successRate := 100 - e.EnhanceLevel*5
	if successRate < 10 {
		successRate = 10
	}

	// 这里应该有随机数判断，简化处理
	e.EnhanceLevel++

	// 强化后属性提升
	e.enhanceAttributes()

	return true
}

// enhanceAttributes 强化属性提升
func (e *Equipment) enhanceAttributes() {
	bonus := e.EnhanceLevel * 2

	e.Attack += bonus
	e.Defense += bonus
	e.MagicAttack += bonus
	e.MagicDefense += bonus
}

// AddGem 镶嵌宝石
func (e *Equipment) AddGem(position int32, gem *Gem) error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if position < 0 || position >= 4 { // 最多4个宝石孔
		return fmt.Errorf("invalid gem position")
	}

	if _, exists := e.Gems[position]; exists {
		return fmt.Errorf("gem position already occupied")
	}

	e.Gems[position] = gem
	return nil
}

// RemoveGem 移除宝石
func (e *Equipment) RemoveGem(position int32) *Gem {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	gem, exists := e.Gems[position]
	if exists {
		delete(e.Gems, position)
	}

	return gem
}

// GetTotalAttributes 获取装备总属性（包括强化和宝石加成）
func (e *Equipment) GetTotalAttributes() EquipmentAttributes {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	attrs := EquipmentAttributes{
		Attack:           e.Attack,
		Defense:          e.Defense,
		MagicAttack:      e.MagicAttack,
		MagicDefense:     e.MagicDefense,
		Accuracy:         e.Accuracy,
		Agility:          e.Agility,
		HPBonus:          e.HPBonus,
		MPBonus:          e.MPBonus,
		CriticalRate:     e.CriticalRate,
		CriticalDamage:   e.CriticalDamage,
		AttackSpeed:      e.AttackSpeed,
		MoveSpeed:        e.MoveSpeed,
		LifeSteal:        e.LifeSteal,
		ManaSteal:        e.ManaSteal,
		FireResistance:   e.FireResistance,
		IceResistance:    e.IceResistance,
		LightResistance:  e.LightResistance,
		PoisonResistance: e.PoisonResistance,
	}

	// 添加宝石属性
	for _, gem := range e.Gems {
		if gem != nil {
			attrs.AddGemAttributes(gem)
		}
	}

	return attrs
}

// EquipmentAttributes 装备属性集合
type EquipmentAttributes struct {
	Attack           int32
	Defense          int32
	MagicAttack      int32
	MagicDefense     int32
	Accuracy         int32
	Agility          int32
	HPBonus          int32
	MPBonus          int32
	CriticalRate     int32
	CriticalDamage   int32
	AttackSpeed      int32
	MoveSpeed        int32
	LifeSteal        int32
	ManaSteal        int32
	FireResistance   int32
	IceResistance    int32
	LightResistance  int32
	PoisonResistance int32
}

// AddGemAttributes 添加宝石属性
func (attrs *EquipmentAttributes) AddGemAttributes(gem *Gem) {
	for attrName, value := range gem.Attributes {
		switch attrName {
		case "attack":
			attrs.Attack += value
		case "defense":
			attrs.Defense += value
		case "magic_attack":
			attrs.MagicAttack += value
		case "magic_defense":
			attrs.MagicDefense += value
		case "accuracy":
			attrs.Accuracy += value
		case "agility":
			attrs.Agility += value
		case "hp_bonus":
			attrs.HPBonus += value
		case "mp_bonus":
			attrs.MPBonus += value
		case "critical_rate":
			attrs.CriticalRate += value
		case "critical_damage":
			attrs.CriticalDamage += value
		}
	}
}
