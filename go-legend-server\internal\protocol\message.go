package protocol

import (
	"encoding/binary"
	"fmt"
)

// MessageType 消息类型定义
type MessageType uint16

// 客户端到游戏服务器的消息类型
const (
	// 认证相关
	ClientGSAuthenticate MessageType = 0x7000
	GSClientAuthenticate MessageType = 0x7001

	// 角色加载通知
	GSClientCharacterLoadedNotify MessageType = 0x7003

	// 聊天相关
	ClientGSMapChat       MessageType = 0x7010
	GSClientMapChat       MessageType = 0x7011
	GSClientMapChatNotify MessageType = 0x7012

	ClientGSPrivateChat       MessageType = 0x7020
	GSClientPrivateChat       MessageType = 0x7021
	GSClientPrivateChatNotify MessageType = 0x7022

	// 移动相关
	ClientGSWalk       MessageType = 0x7030
	GSClientWalk       MessageType = 0x7031
	GSClientWalkNotify MessageType = 0x7032

	ClientGSRun       MessageType = 0x7040
	GSClientRun       MessageType = 0x7041
	GSClientRunNotify MessageType = 0x7042

	// NPC对话
	ClientGSNPCTalk MessageType = 0x7050
	GSClientNPCTalk MessageType = 0x7051

	// 转向
	ClientGSTurn       MessageType = 0x7060
	GSClientTurn       MessageType = 0x7061
	GSClientTurnNotify MessageType = 0x7062

	// 攻击
	ClientGSAttack       MessageType = 0x7070
	GSClientAttack       MessageType = 0x7071
	GSClientAttackNotify MessageType = 0x7072

	// 拾取物品
	ClientGSPickUp MessageType = 0x7080
	GSClientPickUp MessageType = 0x7081

	// 使用物品
	ClientGSBagUseItem MessageType = 0x7090
	GSClientBagUseItem MessageType = 0x7091

	// 脱装备
	ClientGSUndressItem MessageType = 0x70A0
	GSClientUndressItem MessageType = 0x70A1

	// 物品位置交换
	ClientGSItemPositionExchange MessageType = 0x70B0
	GSClientItemPositionExchange MessageType = 0x70B1

	// 使用技能
	ClientGSUseSkill       MessageType = 0x70C0
	GSClientUseSkill       MessageType = 0x70C1
	GSClientUseSkillNotify MessageType = 0x70C2

	// NPC商店
	ClientGSNPCShop MessageType = 0x70D0
	GSClientNPCShop MessageType = 0x70D1

	// NPC购买
	ClientGSNPCBuy MessageType = 0x70E0
	GSClientNPCBuy MessageType = 0x70E1

	// 角色管理
	ClientGSListCharacter   MessageType = 0x7120
	GSClientListCharacter   MessageType = 0x7121
	ClientGSCreateCharacter MessageType = 0x7130
	GSClientCreateCharacter MessageType = 0x7131
	ClientGSEnterGame       MessageType = 0x7140
	GSClientEnterGame       MessageType = 0x7141
	ClientGSDeleteCharacter MessageType = 0x7150
	GSClientDeleteCharacter MessageType = 0x7151

	// 公会相关
	ClientGSListGuild       MessageType = 0x7180
	GSClientListGuild       MessageType = 0x7181
	ClientGSCreateGuild     MessageType = 0x71B0
	GSClientCreateGuild     MessageType = 0x71B1
	ClientGSJoinGuild       MessageType = 0x71C0
	GSClientJoinGuild       MessageType = 0x71C1
	ClientGSGuildChat       MessageType = 0x7240
	GSClientGuildChat       MessageType = 0x7241
	GSClientGuildChatNotify MessageType = 0x7242

	// 攻击模式
	ClientGSChangeAttackMode MessageType = 0x7250
	GSClientChangeAttackMode MessageType = 0x7251

	// 复活
	ClientGSRelive MessageType = 0x7290
	GSClientRelive MessageType = 0x7291

	// 组队相关
	ClientGSCreateGroup MessageType = 0x7320
	GSClientCreateGroup MessageType = 0x7321
	ClientGSLeaveGroup  MessageType = 0x7330
	GSClientLeaveGroup  MessageType = 0x7331
	ClientGSJoinGroup   MessageType = 0x7340
	GSClientJoinGroup   MessageType = 0x7341
	ClientGSGroupChat   MessageType = 0x7390
	GSClientGroupChat   MessageType = 0x7391

	// 世界聊天
	ClientGSWorldChat MessageType = 0x7540
	GSClientWorldChat MessageType = 0x7541

	// 喇叭聊天
	ClientGSHornChat       MessageType = 0x7710
	GSClientHornChat       MessageType = 0x7711
	GSClientHornChatNotify MessageType = 0x7712

	// Ping
	ClientGSPing MessageType = 0x7600
	GSClientPing MessageType = 0x7601
)

// Message 消息接口
type Message interface {
	GetType() MessageType
	Serialize() ([]byte, error)
	Deserialize(data []byte) error
}

// MessageHeader 消息头
type MessageHeader struct {
	Length uint16      // 消息长度（包含头部）
	Type   MessageType // 消息类型
}

// HeaderSize 消息头大小
const HeaderSize = 4

// SerializeHeader 序列化消息头
func SerializeHeader(msgType MessageType, bodyLen int) []byte {
	header := make([]byte, HeaderSize)
	totalLen := HeaderSize + bodyLen
	binary.LittleEndian.PutUint16(header[0:2], uint16(totalLen))
	binary.LittleEndian.PutUint16(header[2:4], uint16(msgType))
	return header
}

// DeserializeHeader 反序列化消息头
func DeserializeHeader(data []byte) (*MessageHeader, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("data too short for header")
	}

	header := &MessageHeader{
		Length: binary.LittleEndian.Uint16(data[0:2]),
		Type:   MessageType(binary.LittleEndian.Uint16(data[2:4])),
	}

	return header, nil
}

// BaseMessage 基础消息结构
type BaseMessage struct {
	Type MessageType
}

func (m *BaseMessage) GetType() MessageType {
	return m.Type
}

// AuthenticateRequest 认证请求
type AuthenticateRequest struct {
	BaseMessage
	SessionID string
	AuthSeed  int32
	PID       int32
	IDFA      string
}

func (m *AuthenticateRequest) Serialize() ([]byte, error) {
	buf := NewBuffer()
	buf.WriteString(m.SessionID)
	buf.WriteInt32(m.AuthSeed)
	buf.WriteInt32(m.PID)
	buf.WriteString(m.IDFA)
	return buf.Bytes(), nil
}

func (m *AuthenticateRequest) Deserialize(data []byte) error {
	buf := NewBufferFromBytes(data)
	var err error
	if m.SessionID, err = buf.ReadString(); err != nil {
		return err
	}
	if m.AuthSeed, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.PID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.IDFA, err = buf.ReadString(); err != nil {
		return err
	}
	return nil
}

// AuthenticateResponse 认证响应
type AuthenticateResponse struct {
	BaseMessage
	Result    int32
	AccountID string
	CharName  string
}

func (m *AuthenticateResponse) Serialize() ([]byte, error) {
	buf := NewBuffer()
	buf.WriteInt32(m.Result)
	buf.WriteString(m.AccountID)
	buf.WriteString(m.CharName)
	return buf.Bytes(), nil
}

func (m *AuthenticateResponse) Deserialize(data []byte) error {
	buf := NewBufferFromBytes(data)
	var err error
	if m.Result, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.AccountID, err = buf.ReadString(); err != nil {
		return err
	}
	if m.CharName, err = buf.ReadString(); err != nil {
		return err
	}
	return nil
}

// ChatMessage 聊天消息
type ChatMessage struct {
	BaseMessage
	SenderID   int32
	SenderName string
	Content    string
	ChatType   int32 // 0: 地图聊天, 1: 私聊, 2: 公会聊天, 3: 组队聊天
}

func (m *ChatMessage) Serialize() ([]byte, error) {
	buf := NewBuffer()
	buf.WriteInt32(m.SenderID)
	buf.WriteString(m.SenderName)
	buf.WriteString(m.Content)
	buf.WriteInt32(m.ChatType)
	return buf.Bytes(), nil
}

func (m *ChatMessage) Deserialize(data []byte) error {
	buf := NewBufferFromBytes(data)
	var err error
	if m.SenderID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.SenderName, err = buf.ReadString(); err != nil {
		return err
	}
	if m.Content, err = buf.ReadString(); err != nil {
		return err
	}
	if m.ChatType, err = buf.ReadInt32(); err != nil {
		return err
	}
	return nil
}

// MoveMessage 移动消息
type MoveMessage struct {
	BaseMessage
	PlayerID  int32
	Direction int32
	X         int32
	Y         int32
	Tag       int32
	MoveType  int32 // 0: 走路, 1: 跑步
}

func (m *MoveMessage) Serialize() ([]byte, error) {
	buf := NewBuffer()
	buf.WriteInt32(m.PlayerID)
	buf.WriteInt32(m.Direction)
	buf.WriteInt32(m.X)
	buf.WriteInt32(m.Y)
	buf.WriteInt32(m.Tag)
	buf.WriteInt32(m.MoveType)
	return buf.Bytes(), nil
}

func (m *MoveMessage) Deserialize(data []byte) error {
	buf := NewBufferFromBytes(data)
	var err error
	if m.PlayerID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.Direction, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.X, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.Y, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.Tag, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.MoveType, err = buf.ReadInt32(); err != nil {
		return err
	}
	return nil
}

// AttackMessage 攻击消息
type AttackMessage struct {
	BaseMessage
	AttackerID int32
	TargetID   int32
	SkillID    int32
	Damage     int32
	X          int32
	Y          int32
}

func (m *AttackMessage) Serialize() ([]byte, error) {
	buf := NewBuffer()
	buf.WriteInt32(m.AttackerID)
	buf.WriteInt32(m.TargetID)
	buf.WriteInt32(m.SkillID)
	buf.WriteInt32(m.Damage)
	buf.WriteInt32(m.X)
	buf.WriteInt32(m.Y)
	return buf.Bytes(), nil
}

func (m *AttackMessage) Deserialize(data []byte) error {
	buf := NewBufferFromBytes(data)
	var err error
	if m.AttackerID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.TargetID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.SkillID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.Damage, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.X, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.Y, err = buf.ReadInt32(); err != nil {
		return err
	}
	return nil
}

// SkillMessage 技能消息
type SkillMessage struct {
	BaseMessage
	CasterID  int32
	TargetID  int32
	SkillID   int32
	X         int32
	Y         int32
	Direction int32
}

func (m *SkillMessage) Serialize() ([]byte, error) {
	buf := NewBuffer()
	buf.WriteInt32(m.CasterID)
	buf.WriteInt32(m.TargetID)
	buf.WriteInt32(m.SkillID)
	buf.WriteInt32(m.X)
	buf.WriteInt32(m.Y)
	buf.WriteInt32(m.Direction)
	return buf.Bytes(), nil
}

func (m *SkillMessage) Deserialize(data []byte) error {
	buf := NewBufferFromBytes(data)
	var err error
	if m.CasterID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.TargetID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.SkillID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.X, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.Y, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.Direction, err = buf.ReadInt32(); err != nil {
		return err
	}
	return nil
}

// NPCTalkMessage NPC对话消息
type NPCTalkMessage struct {
	BaseMessage
	NPCID   int32
	Content string
	Options []string
}

func (m *NPCTalkMessage) Serialize() ([]byte, error) {
	buf := NewBuffer()
	buf.WriteInt32(m.NPCID)
	buf.WriteString(m.Content)
	buf.WriteInt32(int32(len(m.Options)))
	for _, option := range m.Options {
		buf.WriteString(option)
	}
	return buf.Bytes(), nil
}

func (m *NPCTalkMessage) Deserialize(data []byte) error {
	buf := NewBufferFromBytes(data)
	var err error
	if m.NPCID, err = buf.ReadInt32(); err != nil {
		return err
	}
	if m.Content, err = buf.ReadString(); err != nil {
		return err
	}

	optionCount, err := buf.ReadInt32()
	if err != nil {
		return err
	}

	m.Options = make([]string, optionCount)
	for i := int32(0); i < optionCount; i++ {
		if m.Options[i], err = buf.ReadString(); err != nil {
			return err
		}
	}
	return nil
}

// PingMessage Ping消息
type PingMessage struct {
	BaseMessage
	Timestamp  int64
	ClientTime int64
}

func (m *PingMessage) Serialize() ([]byte, error) {
	buf := NewBuffer()
	buf.WriteInt64(m.Timestamp)
	buf.WriteInt64(m.ClientTime)
	return buf.Bytes(), nil
}

func (m *PingMessage) Deserialize(data []byte) error {
	buf := NewBufferFromBytes(data)
	var err error
	if m.Timestamp, err = buf.ReadInt64(); err != nil {
		return err
	}
	if m.ClientTime, err = buf.ReadInt64(); err != nil {
		return err
	}
	return nil
}

// MessageFactory 消息工厂
type MessageFactory struct{}

// CreateMessage 根据消息类型创建消息实例
func (f *MessageFactory) CreateMessage(msgType MessageType) Message {
	switch msgType {
	case ClientGSAuthenticate:
		return &AuthenticateRequest{BaseMessage: BaseMessage{Type: msgType}}
	case GSClientAuthenticate:
		return &AuthenticateResponse{BaseMessage: BaseMessage{Type: msgType}}
	case ClientGSMapChat, GSClientMapChat, GSClientMapChatNotify,
		ClientGSPrivateChat, GSClientPrivateChat, GSClientPrivateChatNotify,
		ClientGSGuildChat, GSClientGuildChat, GSClientGuildChatNotify,
		ClientGSGroupChat, GSClientGroupChat,
		ClientGSWorldChat, GSClientWorldChat,
		ClientGSHornChat, GSClientHornChat, GSClientHornChatNotify:
		return &ChatMessage{BaseMessage: BaseMessage{Type: msgType}}
	case ClientGSWalk, GSClientWalk, GSClientWalkNotify:
		return &MoveMessage{BaseMessage: BaseMessage{Type: msgType}}
	case ClientGSRun, GSClientRun, GSClientRunNotify:
		return &MoveMessage{BaseMessage: BaseMessage{Type: msgType}}
	case ClientGSTurn, GSClientTurn, GSClientTurnNotify:
		return &MoveMessage{BaseMessage: BaseMessage{Type: msgType}}
	case ClientGSAttack, GSClientAttack, GSClientAttackNotify:
		return &AttackMessage{BaseMessage: BaseMessage{Type: msgType}}
	case ClientGSUseSkill, GSClientUseSkill, GSClientUseSkillNotify:
		return &SkillMessage{BaseMessage: BaseMessage{Type: msgType}}
	case ClientGSNPCTalk, GSClientNPCTalk:
		return &NPCTalkMessage{BaseMessage: BaseMessage{Type: msgType}}
	case ClientGSPing, GSClientPing:
		return &PingMessage{BaseMessage: BaseMessage{Type: msgType}}
	default:
		return nil
	}
}

// PackMessage 打包消息
func PackMessage(msg Message) ([]byte, error) {
	body, err := msg.Serialize()
	if err != nil {
		return nil, err
	}

	header := SerializeHeader(msg.GetType(), len(body))
	result := make([]byte, len(header)+len(body))
	copy(result, header)
	copy(result[len(header):], body)

	return result, nil
}

// UnpackMessage 解包消息
func UnpackMessage(data []byte) (Message, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("data too short")
	}

	header, err := DeserializeHeader(data)
	if err != nil {
		return nil, err
	}

	if len(data) < int(header.Length) {
		return nil, fmt.Errorf("incomplete message")
	}

	factory := &MessageFactory{}
	msg := factory.CreateMessage(header.Type)
	if msg == nil {
		return nil, fmt.Errorf("unknown message type: %d", header.Type)
	}

	if err := msg.Deserialize(data[HeaderSize:header.Length]); err != nil {
		return nil, err
	}

	return msg, nil
}
