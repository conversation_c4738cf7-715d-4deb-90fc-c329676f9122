<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="SexyLib"
	ProjectGUID="{A05D8661-B05D-4539-8D24-C21CF3DD12CE}"
	Keyword="Win32Proj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\Vandor;..\..\tools\mirex\zlib\"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/SexyLib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="..\..\Vandor;..\..\tools\mirex\zlib\"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				RuntimeLibrary="2"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/SexyLib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\Application.cpp"
				>
			</File>
			<File
				RelativePath=".\Cryptography.cpp"
				>
			</File>
			<File
				RelativePath=".\DBMssql.cpp"
				>
			</File>
			<File
				RelativePath=".\DBMysql.cpp"
				>
			</File>
			<File
				RelativePath=".\DebugCrash.cpp"
				>
			</File>
			<File
				RelativePath=".\EventQueue.cpp"
				>
			</File>
			<File
				RelativePath=".\EventQueueThread.cpp"
				>
			</File>
			<File
				RelativePath=".\md5.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\MemoryBlock.cpp"
				>
			</File>
			<File
				RelativePath=".\MonitorIOCP.cpp"
				>
			</File>
			<File
				RelativePath=".\RecvBuffer.cpp"
				>
			</File>
			<File
				RelativePath=".\ServerMain.cpp"
				>
			</File>
			<File
				RelativePath=".\ServiceNT.cpp"
				>
			</File>
			<File
				RelativePath=".\SoftwareKey.cpp"
				>
			</File>
			<File
				RelativePath=".\stdafx.cpp"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\StreamBlock.cpp"
				>
			</File>
			<File
				RelativePath=".\StreamFix.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPAccept.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPAcceptEventQueue.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPClient.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPConnect.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPConnectEventQueue.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPSession.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPSessionEventQueue.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPSessionManager.cpp"
				>
			</File>
			<File
				RelativePath=".\TCPSessionMemoryBlock.cpp"
				>
			</File>
			<File
				RelativePath=".\TimerFix.cpp"
				>
			</File>
			<File
				RelativePath=".\ToolsHTTP.cpp"
				>
			</File>
			<File
				RelativePath=".\ToolsPath.cpp"
				>
			</File>
			<File
				RelativePath=".\ToolsString.cpp"
				>
			</File>
			<File
				RelativePath=".\UDPSession.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\leudgrid\ActionBase.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\Application.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\Cryptography.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\DBMssql.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\DBMysql.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\DebugCrash.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\DebugLeak.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\EventBase.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\EventQueue.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\EventQueueThread.h"
				>
			</File>
			<File
				RelativePath=".\md5.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\MemoryBlock.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\MemoryBlockPRD.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\MemoryPool.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\MonitorIOCP.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\RecvBuffer.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\ServerMain.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\ServiceNT.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\SoftwareKey.h"
				>
			</File>
			<File
				RelativePath=".\stdafx.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\StreamBase.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\StreamBaseEx.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\StreamBlock.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\StreamBlock.inl"
				>
			</File>
			<File
				RelativePath=".\leudgrid\StreamBlockPRD.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\StreamFix.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPAccept.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPAcceptEventQueue.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPClient.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPConnect.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPConnectEventQueue.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPRecver.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPSender.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPSession.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPSessionEventQueue.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPSessionManager.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TCPSessionMemoryBlock.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\TimerFix.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\ToolsHTTP.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\ToolsPath.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\ToolsString.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\ToolsThread.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\ToolsUIDGen.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\UDPRecver.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\UDPSender.h"
				>
			</File>
			<File
				RelativePath=".\leudgrid\UDPSession.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
