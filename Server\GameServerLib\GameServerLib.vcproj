<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="GameServerLib"
	ProjectGUID="{74F8D48D-50E2-4AE5-BD7C-A55201216610}"
	Keyword="Win32Proj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;..\lua-5.1.4\src\&quot;"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/GameServerLib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="&quot;..\lua-5.1.4\src\&quot;"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				RuntimeLibrary="2"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/GameServerLib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="ReleaseL|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="&quot;..\lua-5.1.4\src\&quot;"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;LIMIT_RELEASE"
				RuntimeLibrary="2"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/GameServerLib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\ConstsString.cpp"
				>
			</File>
			<File
				RelativePath=".\DomainData.cpp"
				>
			</File>
			<File
				RelativePath=".\DomainGroup.cpp"
				>
			</File>
			<File
				RelativePath=".\DomainGuild.cpp"
				>
			</File>
			<File
				RelativePath=".\DomainPlay.cpp"
				>
			</File>
			<File
				RelativePath=".\GameServerLib.cpp"
				>
			</File>
			<File
				RelativePath=".\GameSession.cpp"
				>
			</File>
			<File
				RelativePath=".\GMCommand.cpp"
				>
			</File>
			<File
				RelativePath=".\GroupGroup.cpp"
				>
			</File>
			<File
				RelativePath=".\GuildGuild.cpp"
				>
			</File>
			<File
				RelativePath=".\ManagerGroup.cpp"
				>
			</File>
			<File
				RelativePath=".\ManagerGuild.cpp"
				>
			</File>
			<File
				RelativePath=".\ManagerItemDef.cpp"
				>
			</File>
			<File
				RelativePath=".\ManagerLevelInfo.cpp"
				>
			</File>
			<File
				RelativePath=".\ManagerMap.cpp"
				>
			</File>
			<File
				RelativePath=".\ManagerMonDef.cpp"
				>
			</File>
			<File
				RelativePath=".\ManagerMonster.cpp"
				>
			</File>
			<File
				RelativePath=".\ManagerSkillDef.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayActiveObject.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayAIObject.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayMap.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayMapItem.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayMapObject.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayMonster.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayMonsterHelper.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayMonsterNomove.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayMonsterRemote.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayMonsterRemoteNomove.cpp"
				>
			</File>
			<File
				RelativePath=".\PlayNpc.cpp"
				>
			</File>
			<File
				RelativePath=".\PlaySlave.cpp"
				>
			</File>
			<File
				RelativePath=".\ScriptLua.cpp"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaGuildBind.cpp"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaMapBind.cpp"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaMonsterBind.cpp"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaNpcBind.cpp"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaPlayerBind.cpp"
				>
			</File>
			<File
				RelativePath=".\stdafx.cpp"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="ReleaseL|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\SubAction.cpp"
				>
			</File>
			<File
				RelativePath=".\SubItem.cpp"
				>
			</File>
			<File
				RelativePath=".\SubSkill.cpp"
				>
			</File>
			<File
				RelativePath=".\SubTask.cpp"
				>
			</File>
			<File
				RelativePath=".\TimerFix.cpp"
				>
			</File>
			<File
				RelativePath=".\TimerFrame.cpp"
				>
			</File>
			<File
				RelativePath=".\TimerSecond.cpp"
				>
			</File>
			<File
				RelativePath=".\UtilString.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\ConstsString.h"
				>
			</File>
			<File
				RelativePath=".\DomainData.h"
				>
			</File>
			<File
				RelativePath=".\DomainGroup.h"
				>
			</File>
			<File
				RelativePath=".\DomainGuild.h"
				>
			</File>
			<File
				RelativePath=".\DomainPlay.h"
				>
			</File>
			<File
				RelativePath=".\GameServerLib.h"
				>
			</File>
			<File
				RelativePath=".\GameSession.h"
				>
			</File>
			<File
				RelativePath=".\GMCommand.h"
				>
			</File>
			<File
				RelativePath=".\GroupGroup.h"
				>
			</File>
			<File
				RelativePath=".\GuildGuild.h"
				>
			</File>
			<File
				RelativePath=".\ManagerGroup.h"
				>
			</File>
			<File
				RelativePath=".\ManagerGuild.h"
				>
			</File>
			<File
				RelativePath=".\ManagerItemDef.h"
				>
			</File>
			<File
				RelativePath=".\ManagerLevelInfo.h"
				>
			</File>
			<File
				RelativePath=".\ManagerMap.h"
				>
			</File>
			<File
				RelativePath=".\ManagerMonDef.h"
				>
			</File>
			<File
				RelativePath=".\ManagerMonster.h"
				>
			</File>
			<File
				RelativePath=".\ManagerSkillDef.h"
				>
			</File>
			<File
				RelativePath=".\PlayActiveObject.h"
				>
			</File>
			<File
				RelativePath=".\PlayAIObject.h"
				>
			</File>
			<File
				RelativePath=".\PlayMap.h"
				>
			</File>
			<File
				RelativePath=".\PlayMapItem.h"
				>
			</File>
			<File
				RelativePath=".\PlayMapObject.h"
				>
			</File>
			<File
				RelativePath=".\PlayMonster.h"
				>
			</File>
			<File
				RelativePath=".\PlayMonsterHelper.h"
				>
			</File>
			<File
				RelativePath=".\PlayMonsterNomove.h"
				>
			</File>
			<File
				RelativePath=".\PlayMonsterRemote.h"
				>
			</File>
			<File
				RelativePath=".\PlayMonsterRemoteNomove.h"
				>
			</File>
			<File
				RelativePath=".\PlayNpc.h"
				>
			</File>
			<File
				RelativePath=".\PlaySlave.h"
				>
			</File>
			<File
				RelativePath=".\ScriptLua.h"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaGuildBind.h"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaMapBind.h"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaMonsterBind.h"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaNpcBind.h"
				>
			</File>
			<File
				RelativePath=".\ScriptLuaPlayerBind.h"
				>
			</File>
			<File
				RelativePath=".\stdafx.h"
				>
			</File>
			<File
				RelativePath=".\SubAction.h"
				>
			</File>
			<File
				RelativePath=".\SubItem.h"
				>
			</File>
			<File
				RelativePath=".\SubSkill.h"
				>
			</File>
			<File
				RelativePath=".\SubTask.h"
				>
			</File>
			<File
				RelativePath=".\TimerFix.h"
				>
			</File>
			<File
				RelativePath=".\TimerFrame.h"
				>
			</File>
			<File
				RelativePath=".\TimerSecond.h"
				>
			</File>
			<File
				RelativePath=".\UtilFun.h"
				>
			</File>
			<File
				RelativePath=".\UtilString.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
