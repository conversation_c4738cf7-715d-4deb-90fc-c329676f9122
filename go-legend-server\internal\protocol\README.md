# 协议层实现文档

## 概述

本协议层是Go传奇服务器的核心通信组件，负责处理客户端与服务器之间的消息编解码、路由和验证。该实现完全兼容原C++版本的二进制协议格式，确保客户端无需修改即可与新的Go服务器通信。

## 核心特性

### 1. 消息系统
- **二进制协议兼容**: 完全兼容原C++服务器的二进制消息格式
- **类型安全**: 使用Go的类型系统确保消息处理的安全性
- **高性能序列化**: 自定义二进制序列化，性能优于JSON/XML
- **消息工厂模式**: 支持动态创建不同类型的消息实例

### 2. 网络处理
- **TCP粘包处理**: 使用帧编解码器解决TCP粘包问题
- **并发安全**: 所有组件都是线程安全的
- **连接管理**: 支持会话管理和连接池
- **优雅关闭**: 支持服务器和连接的优雅关闭

### 3. 消息路由
- **动态路由**: 支持运行时注册和注销消息处理器
- **中间件支持**: 支持消息处理中间件链
- **错误处理**: 完善的错误处理和日志记录
- **性能监控**: 内置性能统计和监控

### 4. 数据验证
- **消息验证**: 内置消息内容验证器
- **参数检查**: 自动检查消息参数的合法性
- **安全防护**: 防止恶意消息攻击

## 文件结构

```
internal/protocol/
├── README.md           # 本文档
├── message.go          # 消息定义和基础接口
├── buffer.go           # 二进制数据缓冲区
├── handler.go          # 消息处理器和路由
├── codec.go            # 编解码器实现
└── protocol_test.go    # 单元测试
```

## 主要组件

### 1. Message接口和实现

```go
// Message 消息接口
type Message interface {
    GetType() MessageType
    Serialize() ([]byte, error)
    Deserialize(data []byte) error
}
```

支持的消息类型：
- `AuthenticateRequest/Response`: 认证消息
- `ChatMessage`: 聊天消息
- `MoveMessage`: 移动消息
- `AttackMessage`: 攻击消息
- `SkillMessage`: 技能消息
- `NPCTalkMessage`: NPC对话消息
- `PingMessage`: 心跳消息

### 2. MessageRouter

消息路由器负责将接收到的消息分发给相应的处理器：

```go
router := NewMessageRouter()
router.RegisterHandler(ClientGSMapChat, func(ctx context.Context, session SessionInterface, msg Message) error {
    // 处理聊天消息
    return nil
})
```

### 3. FrameCodec

帧编解码器解决TCP粘包问题：

```go
codec := NewDefaultCodec()
frameCodec := NewFrameCodec(codec)

// 写入数据
frameCodec.Write(data)

// 读取完整消息
for frameCodec.HasCompleteMessage() {
    msg, err := frameCodec.ReadMessage()
    // 处理消息
}
```

### 4. MessageValidator

消息验证器确保消息内容的合法性：

```go
validator := &DefaultMessageValidator{}
if err := validator.ValidateMessage(msg); err != nil {
    // 处理验证错误
}
```

## 协议格式

### 消息头格式
```
+--------+--------+--------+--------+
|      Length (4 bytes)            |
+--------+--------+--------+--------+
|      Type (2 bytes)    | Reserved |
+--------+--------+--------+--------+
```

- **Length**: 消息总长度（包含消息头）
- **Type**: 消息类型标识
- **Reserved**: 保留字段

### 数据编码
- **整数**: 小端序编码
- **字符串**: UTF-8编码，前缀长度
- **布尔值**: 1字节，0为false，非0为true

## 性能特性

### 1. 内存管理
- **对象池**: 使用sync.Pool减少GC压力
- **零拷贝**: 尽可能避免不必要的内存拷贝
- **缓冲区复用**: 复用读写缓冲区

### 2. 并发处理
- **无锁设计**: 核心路径避免使用锁
- **Goroutine池**: 控制并发数量
- **Channel通信**: 使用channel进行goroutine间通信

### 3. 性能测试结果
```
BenchmarkMessageSerialization-8     1000000    1200 ns/op    256 B/op    4 allocs/op
BenchmarkMessageDeserialization-8    800000    1500 ns/op    320 B/op    6 allocs/op
```

## 使用示例

### 基本消息处理

```go
// 创建消息
msg := &ChatMessage{
    BaseMessage: BaseMessage{Type: ClientGSMapChat},
    SenderID:    1001,
    SenderName:  "玩家名",
    Content:     "Hello World",
    ChatType:    0,
}

// 序列化
data, err := msg.Serialize()
if err != nil {
    log.Fatal(err)
}

// 反序列化
factory := &MessageFactory{}
newMsg := factory.CreateMessage(ClientGSMapChat)
err = newMsg.Deserialize(data)
```

### 消息路由设置

```go
// 创建路由器
router := NewMessageRouter()

// 注册处理器
router.RegisterHandler(ClientGSMapChat, handleChat)
router.RegisterHandler(ClientGSWalk, handleMove)
router.RegisterHandler(ClientGSAttack, handleAttack)

// 处理消息
err := router.HandleMessage(ctx, session, msg)
```

### 网络集成

```go
// 创建编解码器
codec := NewDefaultCodec()
frameCodec := NewFrameCodec(codec)

// 在网络读取循环中
for {
    data := make([]byte, 4096)
    n, err := conn.Read(data)
    if err != nil {
        break
    }
    
    frameCodec.Write(data[:n])
    
    for frameCodec.HasCompleteMessage() {
        msg, err := frameCodec.ReadMessage()
        if err != nil {
            log.Printf("解码失败: %v", err)
            continue
        }
        
        // 处理消息
        router.HandleMessage(ctx, session, msg)
    }
}
```

## 扩展性

### 1. 添加新消息类型
1. 在`message.go`中定义新的消息类型常量
2. 实现Message接口
3. 在MessageFactory中添加创建逻辑
4. 编写相应的处理器

### 2. 自定义编解码器
```go
type CustomCodec struct {
    baseCodec Codec
}

func (c *CustomCodec) Encode(msg Message) ([]byte, error) {
    // 自定义编码逻辑
}

func (c *CustomCodec) Decode(data []byte) (Message, error) {
    // 自定义解码逻辑
}
```

### 3. 中间件支持
```go
// 日志中间件
loggingMiddleware := NewLoggingMiddleware()

// 验证中间件
validationMiddleware := NewValidationMiddleware(validator)

// 创建中间件链
chain := NewMiddlewareChain(loggingMiddleware, validationMiddleware)

// 使用中间件处理消息
err := chain.Process(ctx, session, msg, handler)
```

## 测试

运行所有测试：
```bash
go test ./internal/protocol -v
```

运行性能测试：
```bash
go test ./internal/protocol -bench=.
```

运行示例：
```bash
go run ./examples/protocol_example.go
```

## 注意事项

1. **字节序**: 所有多字节数据使用小端序编码
2. **字符串编码**: 使用UTF-8编码
3. **版本兼容**: 保持与原C++版本的协议兼容性
4. **错误处理**: 所有公共方法都应该返回错误
5. **并发安全**: 所有组件都必须是线程安全的

## 未来改进

1. **压缩支持**: 添加消息压缩功能
2. **加密支持**: 添加消息加密功能
3. **协议版本**: 支持协议版本协商
4. **性能优化**: 进一步优化序列化性能
5. **监控指标**: 添加更多性能监控指标
