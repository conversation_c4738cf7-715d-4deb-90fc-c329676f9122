PROG = 7zDec
CXX = g++
LIB =
RM = rm -f
CFLAGS = -c -O2 -Wall

OBJS = 7zAlloc.o 7zBuf.o 7zBuf2.o 7zCrc.o 7zDecode.o 7zExtract.o 7zHeader.o 7zIn.o 7zItem.o 7zMain.o LzmaDec.o Bra86.o Bcj2.o 7zFile.o 7zStream.o

all: $(PROG)

$(PROG): $(OBJS)
	$(CXX) -o $(PROG) $(LDFLAGS) $(OBJS) $(LIB)

7zAlloc.o: 7zAlloc.c
	$(CXX) $(CFLAGS) 7zAlloc.c

7zBuf.o: ../../7zBuf.c
	$(CXX) $(CFLAGS) ../../7zBuf.c

7zBuf2.o: ../../7zBuf2.c
	$(CXX) $(CFLAGS) ../../7zBuf2.c

7zCrc.o: ../../7zCrc.c
	$(CXX) $(CFLAGS) ../../7zCrc.c

7zDecode.o: 7zDecode.c
	$(CXX) $(CFLAGS) 7zDecode.c

7zExtract.o: 7zExtract.c
	$(CXX) $(CFLAGS) 7zExtract.c

7zHeader.o: 7zHeader.c
	$(CXX) $(CFLAGS) 7zHeader.c

7zIn.o: 7zIn.c
	$(CXX) $(CFLAGS) 7zIn.c

7zItem.o: 7zItem.c
	$(CXX) $(CFLAGS) 7zItem.c

7zMain.o: 7zMain.c
	$(CXX) $(CFLAGS) 7zMain.c

LzmaDec.o: ../../LzmaDec.c
	$(CXX) $(CFLAGS) ../../LzmaDec.c

Bra86.o: ../../Bra86.c
	$(CXX) $(CFLAGS) ../../Bra86.c

Bcj2.o: ../../Bcj2.c
	$(CXX) $(CFLAGS) ../../Bcj2.c

7zFile.o: ../../7zFile.c
	$(CXX) $(CFLAGS) ../../7zFile.c

7zStream.o: ../../7zStream.c
	$(CXX) $(CFLAGS) ../../7zStream.c

clean:
	-$(RM) $(PROG) $(OBJS)

