package main

import (
	"fmt"
	"log"

	"github.com/legend-server/go-legend-server/internal/config"
	"github.com/legend-server/go-legend-server/internal/database"
	"github.com/legend-server/go-legend-server/internal/game"
	"github.com/legend-server/go-legend-server/pkg/logger"
)

func main() {
	// 初始化日志
	logCfg := &config.LogConfig{
		Level:  "debug",
		Format: "console",
		Output: "stdout",
	}
	if err := logger.Init(logCfg); err != nil {
		log.Fatal("Failed to initialize logger:", err)
	}

	// 创建配置
	cfg := &config.GameConfig{
		ExpMultiplier: 1.0,
	}

	// 创建数据库连接（这里使用nil，实际应用中需要真实的数据库）
	db := &database.Database{}

	// 创建游戏世界
	world := game.NewGameWorld(cfg, db)

	// 启动游戏世界
	if err := world.Start(); err != nil {
		log.Fatal("Failed to start game world:", err)
	}
	defer world.Stop()

	fmt.Println("=== 传奇游戏战斗系统演示 ===")

	// 创建两个测试玩家
	warrior := createWarrior()
	wizard := createWizard()

	// 添加玩家到游戏世界
	world.AddPlayer(warrior)
	world.AddPlayer(wizard)

	fmt.Printf("创建玩家:\n")
	fmt.Printf("  战士 %s (等级%d) - 生命值: %d/%d, 攻击力: %d, 防御力: %d\n",
		warrior.CharName, warrior.Level, warrior.HP, warrior.MaxHP, warrior.Attack, warrior.Defense)
	fmt.Printf("  法师 %s (等级%d) - 生命值: %d/%d, 魔法攻击: %d, 魔法防御: %d\n",
		wizard.CharName, wizard.Level, wizard.HP, wizard.MaxHP, wizard.MagicAttack, wizard.MagicDefense)

	// 让战士学习一个技能
	if err := warrior.LearnSkill(1); err != nil {
		fmt.Printf("战士学习技能失败: %v\n", err)
	} else {
		fmt.Printf("战士学习了技能 ID:1\n")
	}

	// 让法师学习一个技能
	if err := wizard.LearnSkill(2); err != nil {
		fmt.Printf("法师学习技能失败: %v\n", err)
	} else {
		fmt.Printf("法师学习了技能 ID:2\n")
	}

	fmt.Println("\n=== 战斗开始 ===")

	// 第一轮：战士普通攻击法师
	fmt.Println("\n第一轮：战士普通攻击法师")
	result, err := world.PlayerAttack(warrior.ID, wizard.ID, 0)
	if err != nil {
		fmt.Printf("攻击失败: %v\n", err)
	} else {
		fmt.Printf("战士对法师造成了 %d 点伤害\n", result.Damage)
		fmt.Printf("法师剩余生命值: %d/%d\n", wizard.HP, wizard.MaxHP)
	}

	// 第二轮：法师使用技能攻击战士
	fmt.Println("\n第二轮：法师使用技能攻击战士")
	if err := world.PlayerUseSkill(wizard.ID, 2, warrior.X, warrior.Y, warrior.ID); err != nil {
		fmt.Printf("技能使用失败: %v\n", err)
	} else {
		fmt.Printf("法师使用了技能攻击战士\n")
		fmt.Printf("法师剩余魔法值: %d/%d\n", wizard.MP, wizard.MaxMP)
	}

	// 第三轮：战士使用技能攻击法师
	fmt.Println("\n第三轮：战士使用技能攻击法师")
	if err := world.PlayerUseSkill(warrior.ID, 1, wizard.X, wizard.Y, wizard.ID); err != nil {
		fmt.Printf("技能使用失败: %v\n", err)
	} else {
		fmt.Printf("战士使用了技能攻击法师\n")
		fmt.Printf("战士剩余魔法值: %d/%d\n", warrior.MP, warrior.MaxMP)
	}

	// 显示最终状态
	fmt.Println("\n=== 战斗结束 ===")
	fmt.Printf("战士最终状态: 生命值 %d/%d, 魔法值 %d/%d, PK值 %d\n",
		warrior.HP, warrior.MaxHP, warrior.MP, warrior.MaxMP, warrior.PKValue)
	fmt.Printf("法师最终状态: 生命值 %d/%d, 魔法值 %d/%d, PK值 %d\n",
		wizard.HP, wizard.MaxHP, wizard.MP, wizard.MaxMP, wizard.PKValue)

	// 演示经验获得
	fmt.Println("\n=== 经验系统演示 ===")
	oldLevel := warrior.Level
	oldExp := warrior.Experience
	warrior.AddExperience(500)
	fmt.Printf("战士获得500点经验\n")
	fmt.Printf("等级变化: %d -> %d\n", oldLevel, warrior.Level)
	fmt.Printf("经验变化: %d -> %d\n", oldExp, warrior.Experience)

	// 演示技能升级
	fmt.Println("\n=== 技能系统演示 ===")
	skill, exists := warrior.GetSkills().GetSkill(1)
	if exists {
		fmt.Printf("战士技能1当前等级: %d, 经验: %d\n", skill.Level, skill.Experience)

		// 手动添加技能经验来演示升级
		skill.Experience += 100
		fmt.Printf("技能获得100点经验后: 等级 %d, 经验 %d\n", skill.Level, skill.Experience)
	}

	fmt.Println("\n演示完成！")
}

// createWarrior 创建战士
func createWarrior() *game.Player {
	warrior := &game.Player{
		ID:           1,
		AccountID:    "account1",
		CharName:     "勇敢的战士",
		Job:          game.JobWarrior,
		Gender:       game.GenderMale,
		Level:        10,
		Experience:   0,
		HP:           150,
		MaxHP:        150,
		MP:           50,
		MaxMP:        50,
		Attack:       60,
		Defense:      40,
		MagicAttack:  20,
		MagicDefense: 30,
		Accuracy:     25,
		Agility:      15,
		MapID:        "0",
		X:            100,
		Y:            100,
		Direction:    4,
		State:        game.PlayerStateOnline,
		AttackMode:   game.AttackModeAll,
		PKValue:      0,
	}

	// 初始化子系统
	warrior.SetInventory(game.NewInventory(warrior))
	warrior.SetSkills(game.NewSkillSet(warrior))
	warrior.SetBuffs(game.NewBuffSet(warrior))

	return warrior
}

// createWizard 创建法师
func createWizard() *game.Player {
	wizard := &game.Player{
		ID:           2,
		AccountID:    "account2",
		CharName:     "智慧法师",
		Job:          game.JobWizard,
		Gender:       game.GenderFemale,
		Level:        10,
		Experience:   0,
		HP:           100,
		MaxHP:        100,
		MP:           120,
		MaxMP:        120,
		Attack:       30,
		Defense:      25,
		MagicAttack:  70,
		MagicDefense: 45,
		Accuracy:     20,
		Agility:      18,
		MapID:        "0",
		X:            102,
		Y:            102,
		Direction:    4,
		State:        game.PlayerStateOnline,
		AttackMode:   game.AttackModeAll,
		PKValue:      0,
	}

	// 初始化子系统
	wizard.SetInventory(game.NewInventory(wizard))
	wizard.SetSkills(game.NewSkillSet(wizard))
	wizard.SetBuffs(game.NewBuffSet(wizard))

	return wizard
}
