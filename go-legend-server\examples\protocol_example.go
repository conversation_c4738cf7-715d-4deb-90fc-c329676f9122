package main

import (
	"context"
	"fmt"
	"log"

	"github.com/legend-server/go-legend-server/internal/protocol"
)

// 演示协议层的使用
func main() {
	fmt.Println("=== Go传奇服务器协议层演示 ===")
	
	// 1. 演示消息创建和序列化
	demonstrateMessageSerialization()
	
	// 2. 演示消息路由
	demonstrateMessageRouting()
	
	// 3. 演示帧编解码器
	demonstrateFrameCodec()
	
	// 4. 演示消息验证
	demonstrateMessageValidation()
}

// 演示消息序列化
func demonstrateMessageSerialization() {
	fmt.Println("\n--- 消息序列化演示 ---")
	
	// 创建聊天消息
	chatMsg := &protocol.ChatMessage{
		BaseMessage: protocol.BaseMessage{Type: protocol.ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "测试玩家",
		Content:     "你好，世界！",
		ChatType:    0,
	}
	
	// 序列化消息
	data, err := chatMsg.Serialize()
	if err != nil {
		log.Fatalf("序列化失败: %v", err)
	}
	
	fmt.Printf("原始消息: %+v\n", chatMsg)
	fmt.Printf("序列化后大小: %d 字节\n", len(data))
	
	// 反序列化
	factory := &protocol.MessageFactory{}
	newMsg := factory.CreateMessage(protocol.ClientGSMapChat)
	if err := newMsg.Deserialize(data); err != nil {
		log.Fatalf("反序列化失败: %v", err)
	}
	
	newChatMsg := newMsg.(*protocol.ChatMessage)
	fmt.Printf("反序列化后: %+v\n", newChatMsg)
	fmt.Printf("内容匹配: %t\n", newChatMsg.Content == chatMsg.Content)
}

// 演示消息路由
func demonstrateMessageRouting() {
	fmt.Println("\n--- 消息路由演示 ---")
	
	// 创建消息路由器
	router := protocol.NewMessageRouter()
	
	// 注册聊天消息处理器
	router.RegisterHandler(protocol.ClientGSMapChat, func(ctx context.Context, session protocol.SessionInterface, msg protocol.Message) error {
		chatMsg := msg.(*protocol.ChatMessage)
		fmt.Printf("处理聊天消息: [%s] %s\n", chatMsg.SenderName, chatMsg.Content)
		return nil
	})
	
	// 注册移动消息处理器
	router.RegisterHandler(protocol.ClientGSWalk, func(ctx context.Context, session protocol.SessionInterface, msg protocol.Message) error {
		moveMsg := msg.(*protocol.MoveMessage)
		fmt.Printf("处理移动消息: 玩家%d移动到(%d, %d)\n", moveMsg.PlayerID, moveMsg.X, moveMsg.Y)
		return nil
	})
	
	// 创建模拟会话
	session := &MockSession{id: 1001}
	
	// 测试聊天消息路由
	chatMsg := &protocol.ChatMessage{
		BaseMessage: protocol.BaseMessage{Type: protocol.ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "路由测试",
		Content:     "这是路由测试消息",
		ChatType:    0,
	}
	
	if err := router.HandleMessage(context.Background(), session, chatMsg); err != nil {
		log.Printf("处理聊天消息失败: %v", err)
	}
	
	// 测试移动消息路由
	moveMsg := &protocol.MoveMessage{
		BaseMessage: protocol.BaseMessage{Type: protocol.ClientGSWalk},
		PlayerID:    1001,
		Direction:   4,
		X:           100,
		Y:           200,
		Tag:         0,
		MoveType:    0,
	}
	
	if err := router.HandleMessage(context.Background(), session, moveMsg); err != nil {
		log.Printf("处理移动消息失败: %v", err)
	}
	
	fmt.Printf("路由器处理器数量: %d\n", router.GetHandlerCount())
}

// 演示帧编解码器
func demonstrateFrameCodec() {
	fmt.Println("\n--- 帧编解码器演示 ---")
	
	codec := protocol.NewDefaultCodec()
	frameCodec := protocol.NewFrameCodec(codec)
	
	// 创建多个消息
	msg1 := &protocol.ChatMessage{
		BaseMessage: protocol.BaseMessage{Type: protocol.ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "玩家1",
		Content:     "第一条消息",
		ChatType:    0,
	}
	
	msg2 := &protocol.ChatMessage{
		BaseMessage: protocol.BaseMessage{Type: protocol.ClientGSMapChat},
		SenderID:    1002,
		SenderName:  "玩家2",
		Content:     "第二条消息",
		ChatType:    0,
	}
	
	// 编码消息
	data1, _ := codec.Encode(msg1)
	data2, _ := codec.Encode(msg2)
	
	// 模拟TCP粘包：合并数据
	combinedData := append(data1, data2...)
	fmt.Printf("合并数据大小: %d 字节\n", len(combinedData))
	
	// 写入帧编解码器
	frameCodec.Write(combinedData)
	
	// 读取消息
	count := 0
	for frameCodec.HasCompleteMessage() {
		msg, err := frameCodec.ReadMessage()
		if err != nil {
			log.Printf("读取消息失败: %v", err)
			break
		}
		
		count++
		chatMsg := msg.(*protocol.ChatMessage)
		fmt.Printf("读取消息%d: [%s] %s\n", count, chatMsg.SenderName, chatMsg.Content)
	}
	
	fmt.Printf("成功处理 %d 条消息\n", count)
}

// 演示消息验证
func demonstrateMessageValidation() {
	fmt.Println("\n--- 消息验证演示 ---")
	
	validator := &protocol.DefaultMessageValidator{}
	
	// 测试有效消息
	validMsg := &protocol.ChatMessage{
		BaseMessage: protocol.BaseMessage{Type: protocol.ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "测试玩家",
		Content:     "这是有效消息",
		ChatType:    0,
	}
	
	if err := validator.ValidateMessage(validMsg); err != nil {
		fmt.Printf("有效消息验证失败: %v\n", err)
	} else {
		fmt.Println("有效消息验证通过")
	}
	
	// 测试无效消息（空内容）
	invalidMsg := &protocol.ChatMessage{
		BaseMessage: protocol.BaseMessage{Type: protocol.ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "测试玩家",
		Content:     "", // 空内容
		ChatType:    0,
	}
	
	if err := validator.ValidateMessage(invalidMsg); err != nil {
		fmt.Printf("无效消息验证失败（预期）: %v\n", err)
	} else {
		fmt.Println("无效消息验证通过（意外）")
	}
	
	// 测试移动消息验证
	validMoveMsg := &protocol.MoveMessage{
		BaseMessage: protocol.BaseMessage{Type: protocol.ClientGSWalk},
		PlayerID:    1001,
		Direction:   4,
		X:           100,
		Y:           200,
		Tag:         0,
		MoveType:    0,
	}
	
	if err := validator.ValidateMessage(validMoveMsg); err != nil {
		fmt.Printf("移动消息验证失败: %v\n", err)
	} else {
		fmt.Println("移动消息验证通过")
	}
}

// MockSession 模拟会话实现
type MockSession struct {
	id       uint64
	userData map[string]interface{}
}

func (s *MockSession) ID() uint64 {
	return s.id
}

func (s *MockSession) SendMessage(msg protocol.Message) error {
	fmt.Printf("发送消息到会话%d: %T\n", s.id, msg)
	return nil
}

func (s *MockSession) GetUserData(key string) (interface{}, bool) {
	if s.userData == nil {
		return nil, false
	}
	value, exists := s.userData[key]
	return value, exists
}

func (s *MockSession) SetUserData(key string, value interface{}) {
	if s.userData == nil {
		s.userData = make(map[string]interface{})
	}
	s.userData[key] = value
}

func (s *MockSession) Close() {
	fmt.Printf("关闭会话%d\n", s.id)
}

func (s *MockSession) RemoteAddr() string {
	return "127.0.0.1:12345"
}
