package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// SQLValidator SQL脚本验证器
type SQLValidator struct {
	errors   []string
	warnings []string
}

// NewSQLValidator 创建验证器
func NewSQLValidator() *SQLValidator {
	return &SQLValidator{
		errors:   make([]string, 0),
		warnings: make([]string, 0),
	}
}

// ValidateFile 验证SQL文件
func (v *SQLValidator) ValidateFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNum := 0
	inComment := false

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行
		if line == "" {
			continue
		}

		// 处理注释
		if strings.HasPrefix(line, "--") {
			continue
		}

		if strings.Contains(line, "/*") {
			inComment = true
		}
		if strings.Contains(line, "*/") {
			inComment = false
			continue
		}
		if inComment {
			continue
		}

		// 验证SQL语句
		v.validateLine(line, lineNum)
	}

	return scanner.Err()
}

// validateLine 验证单行SQL
func (v *SQLValidator) validateLine(line string, lineNum int) {
	line = strings.ToUpper(line)

	// 检查常见的SQL语法错误
	v.checkSyntaxErrors(line, lineNum)

	// 检查最佳实践
	v.checkBestPractices(line, lineNum)
}

// checkSyntaxErrors 检查语法错误
func (v *SQLValidator) checkSyntaxErrors(line string, lineNum int) {
	// 检查未闭合的引号
	singleQuotes := strings.Count(line, "'")
	doubleQuotes := strings.Count(line, "\"")
	backticks := strings.Count(line, "`")

	if singleQuotes%2 != 0 {
		v.errors = append(v.errors, fmt.Sprintf("Line %d: Unclosed single quote", lineNum))
	}
	if doubleQuotes%2 != 0 {
		v.errors = append(v.errors, fmt.Sprintf("Line %d: Unclosed double quote", lineNum))
	}
	if backticks%2 != 0 {
		v.errors = append(v.errors, fmt.Sprintf("Line %d: Unclosed backtick", lineNum))
	}

	// 检查括号匹配
	openParens := strings.Count(line, "(")
	closeParens := strings.Count(line, ")")
	if openParens != closeParens && !strings.Contains(line, ";") {
		v.warnings = append(v.warnings, fmt.Sprintf("Line %d: Unmatched parentheses", lineNum))
	}

	// 检查常见的拼写错误
	commonErrors := map[string]string{
		"CREAT TABLE":    "CREATE TABLE",
		"CREAT DATABASE": "CREATE DATABASE",
		"PRIMAY KEY":     "PRIMARY KEY",
		"FORIEGN KEY":    "FOREIGN KEY",
		"DEFUALT":        "DEFAULT",
		"UNIQE":          "UNIQUE",
		"INDX":           "INDEX",
	}

	for wrong, correct := range commonErrors {
		if strings.Contains(line, wrong) {
			v.errors = append(v.errors, fmt.Sprintf("Line %d: Spelling error '%s', should be '%s'", lineNum, wrong, correct))
		}
	}
}

// checkBestPractices 检查最佳实践
func (v *SQLValidator) checkBestPractices(line string, lineNum int) {
	// 检查是否使用了IF NOT EXISTS
	if strings.Contains(line, "CREATE TABLE") && !strings.Contains(line, "IF NOT EXISTS") {
		v.warnings = append(v.warnings, fmt.Sprintf("Line %d: Consider using 'IF NOT EXISTS' with CREATE TABLE", lineNum))
	}

	// 检查字符集设置
	if strings.Contains(line, "CREATE DATABASE") && !strings.Contains(line, "CHARACTER SET") {
		v.warnings = append(v.warnings, fmt.Sprintf("Line %d: Consider specifying CHARACTER SET for database", lineNum))
	}

	// 检查注释
	if strings.Contains(line, "CREATE TABLE") && !strings.Contains(line, "COMMENT") {
		v.warnings = append(v.warnings, fmt.Sprintf("Line %d: Consider adding table comment", lineNum))
	}
}

// PrintResults 打印验证结果
func (v *SQLValidator) PrintResults() {
	fmt.Println("=== SQL脚本验证结果 ===")

	if len(v.errors) == 0 && len(v.warnings) == 0 {
		fmt.Println("✅ 验证通过！没有发现错误或警告。")
		return
	}

	if len(v.errors) > 0 {
		fmt.Printf("❌ 发现 %d 个错误:\n", len(v.errors))
		for _, err := range v.errors {
			fmt.Printf("  - %s\n", err)
		}
		fmt.Println()
	}

	if len(v.warnings) > 0 {
		fmt.Printf("⚠️  发现 %d 个警告:\n", len(v.warnings))
		for _, warning := range v.warnings {
			fmt.Printf("  - %s\n", warning)
		}
		fmt.Println()
	}
}

// HasErrors 是否有错误
func (v *SQLValidator) HasErrors() bool {
	return len(v.errors) > 0
}

// validateSQLStructure 验证SQL结构
func validateSQLStructure(filename string) error {
	content, err := os.ReadFile(filename)
	if err != nil {
		return err
	}

	sqlContent := string(content)

	// 检查必要的表是否存在
	requiredTables := []string{
		"players",
		"equipments",
		"monsters",
		"skills",
		"drop_tables",
		"maps",
	}

	fmt.Println("🔍 检查必要的表结构...")
	for _, table := range requiredTables {
		pattern := fmt.Sprintf(`CREATE TABLE[^;]*%s[^;]*;`, table)
		matched, _ := regexp.MatchString(pattern, sqlContent)
		if matched {
			fmt.Printf("✅ 表 '%s' 存在\n", table)
		} else {
			fmt.Printf("❌ 表 '%s' 不存在\n", table)
		}
	}

	// 检查初始数据
	fmt.Println("\n🔍 检查初始数据...")
	dataChecks := map[string]string{
		"地图数据": "INSERT.*INTO.*maps",
		"技能数据": "INSERT.*INTO.*skills",
		"掉落数据": "INSERT.*INTO.*drop_tables",
		"怪物数据": "INSERT.*INTO.*monsters",
	}

	for name, pattern := range dataChecks {
		matched, _ := regexp.MatchString(pattern, sqlContent)
		if matched {
			fmt.Printf("✅ %s 存在\n", name)
		} else {
			fmt.Printf("⚠️  %s 不存在\n", name)
		}
	}

	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run validate.go <sql_file>")
		fmt.Println("示例: go run validate.go init.sql")
		os.Exit(1)
	}

	filename := os.Args[1]

	// 检查文件是否存在
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		fmt.Printf("❌ 文件不存在: %s\n", filename)
		os.Exit(1)
	}

	fmt.Printf("📋 验证SQL文件: %s\n\n", filename)

	// 创建验证器
	validator := NewSQLValidator()

	// 验证文件
	if err := validator.ValidateFile(filename); err != nil {
		fmt.Printf("❌ 读取文件失败: %v\n", err)
		os.Exit(1)
	}

	// 打印基本验证结果
	validator.PrintResults()

	// 验证SQL结构
	fmt.Println("=" + strings.Repeat("=", 30))
	if err := validateSQLStructure(filename); err != nil {
		fmt.Printf("❌ 结构验证失败: %v\n", err)
		os.Exit(1)
	}

	// 文件信息
	fmt.Println("=" + strings.Repeat("=", 30))
	if info, err := os.Stat(filename); err == nil {
		fmt.Printf("📊 文件信息:\n")
		fmt.Printf("  - 文件大小: %d 字节\n", info.Size())
		fmt.Printf("  - 修改时间: %s\n", info.ModTime().Format("2006-01-02 15:04:05"))
		if absPath, err := filepath.Abs(filename); err == nil {
			fmt.Printf("  - 文件路径: %s\n", absPath)
		}
	}

	if validator.HasErrors() {
		fmt.Println("\n❌ 验证失败！请修复上述错误后重试。")
		os.Exit(1)
	} else {
		fmt.Println("\n🎉 SQL脚本验证通过！")
	}
}
