# MySQL兼容性修复说明

## 问题描述

在某些MySQL版本中（特别是5.6及更早版本），存在以下限制：
> "Incorrect table definition; there can be only one TIMESTAMP column with CURRENT_TIMESTAMP in DEFAULT or ON UPDATE clause"

这意味着一个表中只能有一个TIMESTAMP列使用CURRENT_TIMESTAMP。

## 解决方案

已将所有`TIMESTAMP`字段修改为`DATETIME`字段，以确保与所有MySQL版本兼容。

### 修改的字段

#### players表
- `last_login_at`: timestamp → datetime
- `last_logout_at`: timestamp → datetime  
- `created_at`: timestamp → datetime
- `updated_at`: timestamp → datetime

#### equipments表
- `bind_time`: timestamp → datetime
- `expire_time`: timestamp → datetime
- `created_at`: timestamp → datetime
- `updated_at`: timestamp → datetime

#### player_equipments表
- `equipped_at`: timestamp → datetime

#### monsters表
- `created_at`: timestamp → datetime
- `updated_at`: timestamp → datetime

#### player_skills表
- `learned_at`: timestamp → datetime

## DATETIME vs TIMESTAMP 对比

| 特性 | DATETIME | TIMESTAMP |
|------|----------|-----------|
| 存储范围 | 1000-01-01 到 9999-12-31 | 1970-01-01 到 2038-01-19 |
| 时区处理 | 不自动转换 | 自动转换到UTC |
| 存储空间 | 8字节 | 4字节 |
| 默认值限制 | 无限制 | MySQL 5.6前有限制 |

## 兼容性

修改后的脚本兼容：
- ✅ MySQL 5.5+
- ✅ MySQL 5.6+  
- ✅ MySQL 5.7+
- ✅ MySQL 8.0+
- ✅ MariaDB 10.0+

## 功能影响

这个修改对应用程序功能没有影响：
- Go的`time.Time`类型可以正常映射到DATETIME
- GORM自动处理DATETIME字段
- 所有时间相关功能保持不变

## 验证

可以通过以下命令验证修复：
```bash
# 检查是否还有timestamp字段
grep -i "timestamp" scripts/init.sql

# 应该没有输出，表示所有timestamp都已替换
```

## 注意事项

1. **时区处理**: 如果应用需要处理多时区，建议在应用层处理时区转换
2. **现有数据**: 如果已有使用TIMESTAMP的数据，需要进行数据迁移
3. **备份**: 在生产环境应用前请备份数据

## 迁移脚本（如果需要）

如果你已经有使用TIMESTAMP的现有数据库，可以使用以下脚本迁移：

```sql
-- 备份表（示例）
CREATE TABLE players_backup AS SELECT * FROM players;

-- 修改字段类型（示例）
ALTER TABLE players 
MODIFY COLUMN last_login_at DATETIME NULL DEFAULT NULL,
MODIFY COLUMN last_logout_at DATETIME NULL DEFAULT NULL,
MODIFY COLUMN created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
MODIFY COLUMN updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
```

## 测试建议

在应用修复后，建议测试：
1. 数据库创建和初始化
2. 时间字段的读写操作
3. 时间相关的查询和排序
4. 应用程序的时间显示功能
