package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/legend-server/go-legend-server/internal/config"
	"github.com/legend-server/go-legend-server/internal/database"
	"github.com/legend-server/go-legend-server/internal/game"
)

var (
	configFile = flag.String("config", "configs/gameserver.yaml", "配置文件路径")
	action     = flag.String("action", "migrate", "操作类型: migrate, seed, reset")
)

func main() {
	flag.Parse()

	fmt.Println("=== Go传奇服务器数据库迁移工具 ===")

	// 加载配置
	cfg, err := config.Load(*configFile)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db := database.NewDatabase(&cfg.Database)
	if err := db.Connect(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	switch *action {
	case "migrate":
		if err := runMigration(db); err != nil {
			log.Fatalf("Migration failed: %v", err)
		}
		fmt.Println("✅ 数据库迁移完成!")

	case "seed":
		if err := runSeeding(db); err != nil {
			log.Fatalf("Seeding failed: %v", err)
		}
		fmt.Println("✅ 测试数据插入完成!")

	case "reset":
		if err := resetDatabase(db); err != nil {
			log.Fatalf("Reset failed: %v", err)
		}
		fmt.Println("✅ 数据库重置完成!")

	default:
		fmt.Printf("未知操作: %s\n", *action)
		fmt.Println("支持的操作: migrate, seed, reset")
		os.Exit(1)
	}
}

// runMigration 运行数据库迁移
func runMigration(db *database.Database) error {
	fmt.Println("🔄 开始数据库迁移...")

	// 自动迁移所有模型
	models := []interface{}{
		&game.Player{},
		// 可以在这里添加更多模型
	}

	for _, model := range models {
		if err := db.GetDB().AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", model, err)
		}
		fmt.Printf("✓ 迁移完成: %T\n", model)
	}

	return nil
}

// runSeeding 插入测试数据
func runSeeding(db *database.Database) error {
	fmt.Println("🌱 开始插入测试数据...")

	// 创建测试玩家
	testPlayers := []*game.Player{
		game.NewPlayer("test001", "测试战士", game.JobWarrior, game.GenderMale),
		game.NewPlayer("test002", "测试法师", game.JobWizard, game.GenderFemale),
		game.NewPlayer("test003", "测试道士", game.JobTaoist, game.GenderMale),
	}

	for _, player := range testPlayers {
		// 检查是否已存在
		var count int64
		db.GetDB().Model(&game.Player{}).Where("char_name = ?", player.CharName).Count(&count)
		if count > 0 {
			fmt.Printf("⚠️  角色已存在: %s\n", player.CharName)
			continue
		}

		if err := db.GetDB().Create(player).Error; err != nil {
			return fmt.Errorf("failed to create test player %s: %w", player.CharName, err)
		}
		fmt.Printf("✓ 创建测试角色: %s (职业: %d)\n", player.CharName, player.Job)
	}

	return nil
}

// resetDatabase 重置数据库
func resetDatabase(db *database.Database) error {
	fmt.Println("🔄 开始重置数据库...")
	fmt.Println("⚠️  警告: 这将删除所有数据!")

	// 确认操作
	fmt.Print("请输入 'YES' 确认重置: ")
	var confirm string
	fmt.Scanln(&confirm)
	
	if confirm != "YES" {
		fmt.Println("❌ 操作已取消")
		return nil
	}

	// 删除所有表
	tables := []string{
		"player_skills",
		"player_equipments", 
		"equipments",
		"players",
		"monsters",
		"skills",
		"drop_items",
		"drop_tables",
		"equipment_templates",
		"teleports",
		"maps",
	}

	for _, table := range tables {
		if err := db.GetDB().Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", table)).Error; err != nil {
			fmt.Printf("⚠️  删除表失败 %s: %v\n", table, err)
		} else {
			fmt.Printf("✓ 删除表: %s\n", table)
		}
	}

	// 重新运行迁移
	return runMigration(db)
}
