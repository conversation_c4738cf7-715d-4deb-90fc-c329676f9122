package game

import (
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// DropType 掉落类型
type DropType int32

const (
	DropTypeEquipment  DropType = iota // 装备
	DropTypeItem                       // 物品
	DropTypeGold                       // 金币
	DropTypeExperience                 // 经验
)

// DropRarity 掉落稀有度
type DropRarity int32

const (
	DropRarityCommon    DropRarity = iota // 普通
	DropRarityUncommon                    // 不凡
	DropRarityRare                        // 稀有
	DropRarityEpic                        // 史诗
	DropRarityLegendary                   // 传说
	DropRarityArtifact                    // 神器
)

// DropItem 掉落物品定义
type DropItem struct {
	ID          int32      `json:"id"`           // 掉落ID
	Type        DropType   `json:"type"`         // 掉落类型
	ItemID      int32      `json:"item_id"`      // 物品ID
	MinQuantity int32      `json:"min_quantity"` // 最小数量
	MaxQuantity int32      `json:"max_quantity"` // 最大数量
	Weight      int32      `json:"weight"`       // 权重
	Rarity      DropRarity `json:"rarity"`       // 稀有度
	MinLevel    int32      `json:"min_level"`    // 最小等级要求
	MaxLevel    int32      `json:"max_level"`    // 最大等级要求
	RequiredJob JobType    `json:"required_job"` // 职业要求
	DropRate    float64    `json:"drop_rate"`    // 掉落概率 (0.0-1.0)
}

// DropTable 掉落表
type DropTable struct {
	ID          int32      `json:"id"`           // 掉落表ID
	Name        string     `json:"name"`         // 掉落表名称
	Description string     `json:"description"`  // 描述
	Items       []DropItem `json:"items"`        // 掉落物品列表
	TotalWeight int32      `json:"total_weight"` // 总权重
}

// EquipmentTemplate 装备模板
type EquipmentTemplate struct {
	ID              int32               `json:"id"`                // 模板ID
	Name            string              `json:"name"`              // 装备名称
	Type            EquipmentType       `json:"type"`              // 装备类型
	BaseQuality     EquipmentQuality    `json:"base_quality"`      // 基础品质
	MinLevel        int32               `json:"min_level"`         // 最小等级
	MaxLevel        int32               `json:"max_level"`         // 最大等级
	RequiredJob     JobType             `json:"required_job"`      // 职业要求
	RequiredGender  GenderType          `json:"required_gender"`   // 性别要求
	BaseAttributes  EquipmentAttributes `json:"base_attributes"`   // 基础属性
	AttributeRanges AttributeRanges     `json:"attribute_ranges"`  // 属性范围
	CanEnhance      bool                `json:"can_enhance"`       // 是否可强化
	MaxEnhanceLevel int32               `json:"max_enhance_level"` // 最大强化等级
	SocketCount     int32               `json:"socket_count"`      // 插槽数量
}

// AttributeRanges 属性范围定义
type AttributeRanges struct {
	AttackMin       int32 `json:"attack_min"`
	AttackMax       int32 `json:"attack_max"`
	DefenseMin      int32 `json:"defense_min"`
	DefenseMax      int32 `json:"defense_max"`
	MagicAttackMin  int32 `json:"magic_attack_min"`
	MagicAttackMax  int32 `json:"magic_attack_max"`
	MagicDefenseMin int32 `json:"magic_defense_min"`
	MagicDefenseMax int32 `json:"magic_defense_max"`
	HPBonusMin      int32 `json:"hp_bonus_min"`
	HPBonusMax      int32 `json:"hp_bonus_max"`
	MPBonusMin      int32 `json:"mp_bonus_min"`
	MPBonusMax      int32 `json:"mp_bonus_max"`
	AccuracyMin     int32 `json:"accuracy_min"`
	AccuracyMax     int32 `json:"accuracy_max"`
	AgilityMin      int32 `json:"agility_min"`
	AgilityMax      int32 `json:"agility_max"`
}

// DroppedItem 掉落的物品实例
type DroppedItem struct {
	ID         uint64     `json:"id"`          // 掉落物品唯一ID
	Type       DropType   `json:"type"`        // 掉落类型
	ItemID     int32      `json:"item_id"`     // 物品ID
	Quantity   int32      `json:"quantity"`    // 数量
	Equipment  *Equipment `json:"equipment"`   // 装备实例（如果是装备）
	MapID      string     `json:"map_id"`      // 地图ID
	X          int32      `json:"x"`           // X坐标
	Y          int32      `json:"y"`           // Y坐标
	DropTime   time.Time  `json:"drop_time"`   // 掉落时间
	OwnerID    uint64     `json:"owner_id"`    // 拾取者ID（0表示所有人可拾取）
	ExpireTime time.Time  `json:"expire_time"` // 过期时间
}

// DropSystem 掉落系统
type DropSystem struct {
	mutex              sync.RWMutex
	dropTables         map[int32]*DropTable         // 掉落表
	equipmentTemplates map[int32]*EquipmentTemplate // 装备模板
	droppedItems       map[uint64]*DroppedItem      // 掉落的物品
	nextDropID         uint64                       // 下一个掉落物品ID
	logger             *zap.Logger
}

// NewDropSystem 创建掉落系统
func NewDropSystem() *DropSystem {
	ds := &DropSystem{
		dropTables:         make(map[int32]*DropTable),
		equipmentTemplates: make(map[int32]*EquipmentTemplate),
		droppedItems:       make(map[uint64]*DroppedItem),
		nextDropID:         1,
		logger:             logger.GetLogger(),
	}

	// 初始化默认掉落表和装备模板
	ds.initializeDefaultData()

	return ds
}

// initializeDefaultData 初始化默认数据
func (ds *DropSystem) initializeDefaultData() {
	// 初始化装备模板
	ds.initializeEquipmentTemplates()

	// 初始化掉落表
	ds.initializeDropTables()
}

// initializeEquipmentTemplates 初始化装备模板
func (ds *DropSystem) initializeEquipmentTemplates() {
	templates := []*EquipmentTemplate{
		// 武器模板
		{
			ID:              1001,
			Name:            "新手剑",
			Type:            EquipTypeWeapon,
			BaseQuality:     QualityCommon,
			MinLevel:        1,
			MaxLevel:        10,
			RequiredJob:     JobWarrior,
			RequiredGender:  GenderAll,
			BaseAttributes:  EquipmentAttributes{Attack: 15, Accuracy: 5},
			AttributeRanges: AttributeRanges{AttackMin: 10, AttackMax: 20, AccuracyMin: 3, AccuracyMax: 8},
			CanEnhance:      true,
			MaxEnhanceLevel: 10,
			SocketCount:     1,
		},
		{
			ID:              1002,
			Name:            "精钢剑",
			Type:            EquipTypeWeapon,
			BaseQuality:     QualityUncommon,
			MinLevel:        10,
			MaxLevel:        20,
			RequiredJob:     JobWarrior,
			RequiredGender:  GenderAll,
			BaseAttributes:  EquipmentAttributes{Attack: 35, Accuracy: 10},
			AttributeRanges: AttributeRanges{AttackMin: 25, AttackMax: 45, AccuracyMin: 8, AccuracyMax: 15},
			CanEnhance:      true,
			MaxEnhanceLevel: 15,
			SocketCount:     2,
		},
		{
			ID:              1003,
			Name:            "屠龙刀",
			Type:            EquipTypeWeapon,
			BaseQuality:     QualityLegendary,
			MinLevel:        30,
			MaxLevel:        50,
			RequiredJob:     JobWarrior,
			RequiredGender:  GenderAll,
			BaseAttributes:  EquipmentAttributes{Attack: 80, Accuracy: 20, CriticalRate: 10},
			AttributeRanges: AttributeRanges{AttackMin: 70, AttackMax: 100, AccuracyMin: 15, AccuracyMax: 25},
			CanEnhance:      true,
			MaxEnhanceLevel: 20,
			SocketCount:     3,
		},
		// 法师武器
		{
			ID:              2001,
			Name:            "新手法杖",
			Type:            EquipTypeWeapon,
			BaseQuality:     QualityCommon,
			MinLevel:        1,
			MaxLevel:        10,
			RequiredJob:     JobWizard,
			RequiredGender:  GenderAll,
			BaseAttributes:  EquipmentAttributes{MagicAttack: 20, MPBonus: 15},
			AttributeRanges: AttributeRanges{MagicAttackMin: 15, MagicAttackMax: 25, MPBonusMin: 10, MPBonusMax: 20},
			CanEnhance:      true,
			MaxEnhanceLevel: 10,
			SocketCount:     1,
		},
		{
			ID:              2002,
			Name:            "魔法法杖",
			Type:            EquipTypeWeapon,
			BaseQuality:     QualityRare,
			MinLevel:        15,
			MaxLevel:        25,
			RequiredJob:     JobWizard,
			RequiredGender:  GenderAll,
			BaseAttributes:  EquipmentAttributes{MagicAttack: 45, MPBonus: 40},
			AttributeRanges: AttributeRanges{MagicAttackMin: 35, MagicAttackMax: 55, MPBonusMin: 30, MPBonusMax: 50},
			CanEnhance:      true,
			MaxEnhanceLevel: 15,
			SocketCount:     2,
		},
		// 防具模板
		{
			ID:              3001,
			Name:            "布衣",
			Type:            EquipTypeArmor,
			BaseQuality:     QualityCommon,
			MinLevel:        1,
			MaxLevel:        8,
			RequiredJob:     JobAll,
			RequiredGender:  GenderAll,
			BaseAttributes:  EquipmentAttributes{Defense: 8, HPBonus: 20},
			AttributeRanges: AttributeRanges{DefenseMin: 5, DefenseMax: 12, HPBonusMin: 15, HPBonusMax: 25},
			CanEnhance:      true,
			MaxEnhanceLevel: 8,
			SocketCount:     1,
		},
		{
			ID:              3002,
			Name:            "皮甲",
			Type:            EquipTypeArmor,
			BaseQuality:     QualityUncommon,
			MinLevel:        8,
			MaxLevel:        18,
			RequiredJob:     JobAll,
			RequiredGender:  GenderAll,
			BaseAttributes:  EquipmentAttributes{Defense: 25, HPBonus: 40},
			AttributeRanges: AttributeRanges{DefenseMin: 20, DefenseMax: 30, HPBonusMin: 30, HPBonusMax: 50},
			CanEnhance:      true,
			MaxEnhanceLevel: 12,
			SocketCount:     2,
		},
	}

	for _, template := range templates {
		ds.equipmentTemplates[template.ID] = template
	}
}

// initializeDropTables 初始化掉落表
func (ds *DropSystem) initializeDropTables() {
	// 低级怪物掉落表
	lowLevelTable := &DropTable{
		ID:          1,
		Name:        "低级怪物掉落",
		Description: "1-10级怪物的掉落表",
		Items: []DropItem{
			{ID: 1, Type: DropTypeEquipment, ItemID: 1001, MinQuantity: 1, MaxQuantity: 1, Weight: 5, Rarity: DropRarityCommon, DropRate: 0.1},
			{ID: 2, Type: DropTypeEquipment, ItemID: 2001, MinQuantity: 1, MaxQuantity: 1, Weight: 5, Rarity: DropRarityCommon, DropRate: 0.1},
			{ID: 3, Type: DropTypeEquipment, ItemID: 3001, MinQuantity: 1, MaxQuantity: 1, Weight: 8, Rarity: DropRarityCommon, DropRate: 0.15},
			{ID: 4, Type: DropTypeGold, ItemID: 0, MinQuantity: 10, MaxQuantity: 50, Weight: 50, Rarity: DropRarityCommon, DropRate: 0.8},
			{ID: 5, Type: DropTypeExperience, ItemID: 0, MinQuantity: 20, MaxQuantity: 100, Weight: 30, Rarity: DropRarityCommon, DropRate: 1.0},
		},
	}

	// 中级怪物掉落表
	midLevelTable := &DropTable{
		ID:          2,
		Name:        "中级怪物掉落",
		Description: "10-25级怪物的掉落表",
		Items: []DropItem{
			{ID: 6, Type: DropTypeEquipment, ItemID: 1002, MinQuantity: 1, MaxQuantity: 1, Weight: 3, Rarity: DropRarityUncommon, DropRate: 0.08},
			{ID: 7, Type: DropTypeEquipment, ItemID: 2002, MinQuantity: 1, MaxQuantity: 1, Weight: 3, Rarity: DropRarityRare, DropRate: 0.05},
			{ID: 8, Type: DropTypeEquipment, ItemID: 3002, MinQuantity: 1, MaxQuantity: 1, Weight: 5, Rarity: DropRarityUncommon, DropRate: 0.12},
			{ID: 9, Type: DropTypeGold, ItemID: 0, MinQuantity: 50, MaxQuantity: 200, Weight: 40, Rarity: DropRarityCommon, DropRate: 0.9},
			{ID: 10, Type: DropTypeExperience, ItemID: 0, MinQuantity: 100, MaxQuantity: 500, Weight: 25, Rarity: DropRarityCommon, DropRate: 1.0},
		},
	}

	// BOSS掉落表
	bossTable := &DropTable{
		ID:          3,
		Name:        "BOSS掉落",
		Description: "BOSS怪物的掉落表",
		Items: []DropItem{
			{ID: 11, Type: DropTypeEquipment, ItemID: 1003, MinQuantity: 1, MaxQuantity: 1, Weight: 1, Rarity: DropRarityLegendary, DropRate: 0.02},
			{ID: 12, Type: DropTypeEquipment, ItemID: 1002, MinQuantity: 1, MaxQuantity: 1, Weight: 5, Rarity: DropRarityUncommon, DropRate: 0.3},
			{ID: 13, Type: DropTypeEquipment, ItemID: 2002, MinQuantity: 1, MaxQuantity: 1, Weight: 3, Rarity: DropRarityRare, DropRate: 0.2},
			{ID: 14, Type: DropTypeGold, ItemID: 0, MinQuantity: 500, MaxQuantity: 2000, Weight: 20, Rarity: DropRarityCommon, DropRate: 1.0},
			{ID: 15, Type: DropTypeExperience, ItemID: 0, MinQuantity: 1000, MaxQuantity: 5000, Weight: 15, Rarity: DropRarityCommon, DropRate: 1.0},
		},
	}

	// 计算总权重
	ds.calculateTotalWeight(lowLevelTable)
	ds.calculateTotalWeight(midLevelTable)
	ds.calculateTotalWeight(bossTable)

	ds.dropTables[1] = lowLevelTable
	ds.dropTables[2] = midLevelTable
	ds.dropTables[3] = bossTable
}

// calculateTotalWeight 计算掉落表总权重
func (ds *DropSystem) calculateTotalWeight(table *DropTable) {
	total := int32(0)
	for _, item := range table.Items {
		total += item.Weight
	}
	table.TotalWeight = total
}

// GenerateEquipment 根据模板生成装备
func (ds *DropSystem) GenerateEquipment(templateID int32, playerLevel int32) (*Equipment, error) {
	ds.mutex.RLock()
	template, exists := ds.equipmentTemplates[templateID]
	ds.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("equipment template %d not found", templateID)
	}

	// 创建基础装备
	equipment := NewEquipment(templateID, template.Name, template.Type, template.BaseQuality)

	// 设置基础属性
	equipment.RequiredLevel = ds.randomBetween(template.MinLevel, template.MaxLevel)
	equipment.RequiredJob = template.RequiredJob
	equipment.RequiredGender = template.RequiredGender

	// 随机生成属性
	ds.generateRandomAttributes(equipment, template)

	// 随机品质提升
	ds.randomizeQuality(equipment, playerLevel)

	// 随机强化等级
	if template.CanEnhance {
		ds.randomizeEnhancement(equipment, template.MaxEnhanceLevel, playerLevel)
	}

	// 初始化宝石插槽
	equipment.Gems = make(map[int32]*Gem)

	ds.logger.Debug("Generated equipment",
		zap.String("name", equipment.Name),
		zap.Int32("template_id", templateID),
		zap.Int32("quality", int32(equipment.Quality)),
		zap.Int32("enhance_level", equipment.EnhanceLevel))

	return equipment, nil
}

// generateRandomAttributes 生成随机属性
func (ds *DropSystem) generateRandomAttributes(equipment *Equipment, template *EquipmentTemplate) {
	ranges := template.AttributeRanges

	// 攻击力
	if ranges.AttackMax > 0 {
		equipment.Attack = ds.randomBetween(ranges.AttackMin, ranges.AttackMax)
	}

	// 防御力
	if ranges.DefenseMax > 0 {
		equipment.Defense = ds.randomBetween(ranges.DefenseMin, ranges.DefenseMax)
	}

	// 魔法攻击力
	if ranges.MagicAttackMax > 0 {
		equipment.MagicAttack = ds.randomBetween(ranges.MagicAttackMin, ranges.MagicAttackMax)
	}

	// 魔法防御力
	if ranges.MagicDefenseMax > 0 {
		equipment.MagicDefense = ds.randomBetween(ranges.MagicDefenseMin, ranges.MagicDefenseMax)
	}

	// 生命值加成
	if ranges.HPBonusMax > 0 {
		equipment.HPBonus = ds.randomBetween(ranges.HPBonusMin, ranges.HPBonusMax)
	}

	// 魔法值加成
	if ranges.MPBonusMax > 0 {
		equipment.MPBonus = ds.randomBetween(ranges.MPBonusMin, ranges.MPBonusMax)
	}

	// 精确度
	if ranges.AccuracyMax > 0 {
		equipment.Accuracy = ds.randomBetween(ranges.AccuracyMin, ranges.AccuracyMax)
	}

	// 敏捷度
	if ranges.AgilityMax > 0 {
		equipment.Agility = ds.randomBetween(ranges.AgilityMin, ranges.AgilityMax)
	}
}

// randomizeQuality 随机化品质
func (ds *DropSystem) randomizeQuality(equipment *Equipment, playerLevel int32) {
	// 基于玩家等级和随机数决定是否提升品质
	baseChance := float64(playerLevel) / 100.0 // 等级越高，品质提升概率越大

	for {
		if equipment.Quality >= QualityArtifact {
			break // 已经是最高品质
		}

		// 计算品质提升概率
		upgradeChance := baseChance / float64(equipment.Quality+1)
		if upgradeChance > 0.5 {
			upgradeChance = 0.5 // 最大50%概率
		}

		if rand.Float64() < upgradeChance {
			equipment.Quality++
			// 品质提升时，属性也相应提升
			ds.enhanceAttributesForQuality(equipment)
		} else {
			break
		}
	}
}

// enhanceAttributesForQuality 根据品质提升属性
func (ds *DropSystem) enhanceAttributesForQuality(equipment *Equipment) {
	multiplier := 1.2 // 每提升一个品质，属性增加20%

	equipment.Attack = int32(float64(equipment.Attack) * multiplier)
	equipment.Defense = int32(float64(equipment.Defense) * multiplier)
	equipment.MagicAttack = int32(float64(equipment.MagicAttack) * multiplier)
	equipment.MagicDefense = int32(float64(equipment.MagicDefense) * multiplier)
	equipment.HPBonus = int32(float64(equipment.HPBonus) * multiplier)
	equipment.MPBonus = int32(float64(equipment.MPBonus) * multiplier)
	equipment.Accuracy = int32(float64(equipment.Accuracy) * multiplier)
	equipment.Agility = int32(float64(equipment.Agility) * multiplier)
}

// randomizeEnhancement 随机强化等级
func (ds *DropSystem) randomizeEnhancement(equipment *Equipment, maxLevel int32, playerLevel int32) {
	// 基于玩家等级决定强化等级
	maxPossibleLevel := playerLevel / 5 // 每5级可能获得1级强化
	if maxPossibleLevel > maxLevel {
		maxPossibleLevel = maxLevel
	}

	if maxPossibleLevel > 0 {
		// 随机强化等级，但偏向较低等级
		for i := int32(0); i < maxPossibleLevel; i++ {
			chance := 0.3 / float64(i+1) // 强化等级越高，概率越低
			if rand.Float64() < chance {
				equipment.EnhanceLevel++
			} else {
				break
			}
		}

		// 应用强化效果
		for i := int32(0); i < equipment.EnhanceLevel; i++ {
			equipment.Enhance()
		}
	}
}

// randomBetween 生成指定范围内的随机数
func (ds *DropSystem) randomBetween(min, max int32) int32 {
	if min >= max {
		return min
	}
	return min + rand.Int31n(max-min+1)
}

// ProcessDrop 处理掉落
func (ds *DropSystem) ProcessDrop(tableID int32, killerLevel int32, killerJob JobType, mapID string, x, y int32) []*DroppedItem {
	ds.mutex.RLock()
	table, exists := ds.dropTables[tableID]
	ds.mutex.RUnlock()

	if !exists {
		ds.logger.Warn("Drop table not found", zap.Int32("table_id", tableID))
		return nil
	}

	var droppedItems []*DroppedItem

	// 遍历掉落表中的每个物品
	for _, dropItem := range table.Items {
		// 检查掉落概率
		if !ds.checkDropRate(dropItem.DropRate) {
			continue
		}

		// 检查等级要求
		if !ds.checkLevelRequirement(dropItem, killerLevel) {
			continue
		}

		// 权重随机选择
		if !ds.checkWeightedDrop(dropItem, table) {
			continue
		}

		// 生成掉落物品
		dropped := ds.createDroppedItem(dropItem, killerLevel, killerJob, mapID, x, y)
		if dropped != nil {
			droppedItems = append(droppedItems, dropped)
		}
	}

	ds.logger.Debug("Processed drop",
		zap.Int32("table_id", tableID),
		zap.Int32("killer_level", killerLevel),
		zap.Int("dropped_count", len(droppedItems)))

	return droppedItems
}

// checkDropRate 检查掉落概率
func (ds *DropSystem) checkDropRate(dropRate float64) bool {
	return rand.Float64() < dropRate
}

// checkLevelRequirement 检查等级要求
func (ds *DropSystem) checkLevelRequirement(dropItem DropItem, killerLevel int32) bool {
	if dropItem.MinLevel > 0 && killerLevel < dropItem.MinLevel {
		return false
	}
	if dropItem.MaxLevel > 0 && killerLevel > dropItem.MaxLevel {
		return false
	}
	return true
}

// checkWeightedDrop 权重随机选择
func (ds *DropSystem) checkWeightedDrop(dropItem DropItem, table *DropTable) bool {
	// 基于稀有度调整权重
	adjustedWeight := ds.adjustWeightByRarity(dropItem.Weight, dropItem.Rarity)

	// 随机数判断
	randomValue := rand.Int31n(table.TotalWeight)
	return randomValue < adjustedWeight
}

// adjustWeightByRarity 根据稀有度调整权重
func (ds *DropSystem) adjustWeightByRarity(baseWeight int32, rarity DropRarity) int32 {
	switch rarity {
	case DropRarityCommon:
		return baseWeight
	case DropRarityUncommon:
		return baseWeight * 8 / 10 // 80%
	case DropRarityRare:
		return baseWeight * 6 / 10 // 60%
	case DropRarityEpic:
		return baseWeight * 4 / 10 // 40%
	case DropRarityLegendary:
		return baseWeight * 2 / 10 // 20%
	case DropRarityArtifact:
		return baseWeight * 1 / 10 // 10%
	default:
		return baseWeight
	}
}

// createDroppedItem 创建掉落物品实例
func (ds *DropSystem) createDroppedItem(dropItem DropItem, killerLevel int32, killerJob JobType, mapID string, x, y int32) *DroppedItem {
	ds.mutex.Lock()
	dropID := ds.nextDropID
	ds.nextDropID++
	ds.mutex.Unlock()

	// 计算数量
	quantity := ds.randomBetween(dropItem.MinQuantity, dropItem.MaxQuantity)

	dropped := &DroppedItem{
		ID:         dropID,
		Type:       dropItem.Type,
		ItemID:     dropItem.ItemID,
		Quantity:   quantity,
		MapID:      mapID,
		X:          x,
		Y:          y,
		DropTime:   time.Now(),
		OwnerID:    0,                               // 0表示所有人可拾取
		ExpireTime: time.Now().Add(5 * time.Minute), // 5分钟后消失
	}

	// 如果是装备，生成装备实例
	if dropItem.Type == DropTypeEquipment {
		equipment, err := ds.GenerateEquipment(dropItem.ItemID, killerLevel)
		if err != nil {
			ds.logger.Error("Failed to generate equipment",
				zap.Int32("template_id", dropItem.ItemID),
				zap.Error(err))
			return nil
		}
		dropped.Equipment = equipment
	}

	// 存储掉落物品
	ds.mutex.Lock()
	ds.droppedItems[dropID] = dropped
	ds.mutex.Unlock()

	ds.logger.Info("Item dropped",
		zap.Uint64("drop_id", dropID),
		zap.Int32("type", int32(dropItem.Type)),
		zap.Int32("item_id", dropItem.ItemID),
		zap.Int32("quantity", quantity),
		zap.String("map_id", mapID))

	return dropped
}

// GetDroppedItem 获取掉落物品
func (ds *DropSystem) GetDroppedItem(dropID uint64) (*DroppedItem, bool) {
	ds.mutex.RLock()
	defer ds.mutex.RUnlock()

	item, exists := ds.droppedItems[dropID]
	return item, exists
}

// PickupItem 拾取物品
func (ds *DropSystem) PickupItem(dropID uint64, playerID uint64) (*DroppedItem, error) {
	ds.mutex.Lock()
	defer ds.mutex.Unlock()

	item, exists := ds.droppedItems[dropID]
	if !exists {
		return nil, fmt.Errorf("dropped item %d not found", dropID)
	}

	// 检查是否过期
	if time.Now().After(item.ExpireTime) {
		delete(ds.droppedItems, dropID)
		return nil, fmt.Errorf("dropped item %d has expired", dropID)
	}

	// 检查拾取权限
	if item.OwnerID != 0 && item.OwnerID != playerID {
		return nil, fmt.Errorf("no permission to pickup item %d", dropID)
	}

	// 移除掉落物品
	delete(ds.droppedItems, dropID)

	ds.logger.Info("Item picked up",
		zap.Uint64("drop_id", dropID),
		zap.Uint64("player_id", playerID),
		zap.Int32("type", int32(item.Type)),
		zap.Int32("item_id", item.ItemID))

	return item, nil
}

// CleanupExpiredItems 清理过期物品
func (ds *DropSystem) CleanupExpiredItems() {
	ds.mutex.Lock()
	defer ds.mutex.Unlock()

	now := time.Now()
	var expiredIDs []uint64

	for id, item := range ds.droppedItems {
		if now.After(item.ExpireTime) {
			expiredIDs = append(expiredIDs, id)
		}
	}

	for _, id := range expiredIDs {
		delete(ds.droppedItems, id)
	}

	if len(expiredIDs) > 0 {
		ds.logger.Debug("Cleaned up expired items", zap.Int("count", len(expiredIDs)))
	}
}

// GetAllDroppedItems 获取所有掉落物品（用于调试和演示）
func (ds *DropSystem) GetAllDroppedItems() []*DroppedItem {
	ds.mutex.RLock()
	defer ds.mutex.RUnlock()

	items := make([]*DroppedItem, 0, len(ds.droppedItems))
	for _, item := range ds.droppedItems {
		items = append(items, item)
	}

	return items
}

// GetDroppedItemCount 获取掉落物品数量
func (ds *DropSystem) GetDroppedItemCount() int {
	ds.mutex.RLock()
	defer ds.mutex.RUnlock()

	return len(ds.droppedItems)
}
