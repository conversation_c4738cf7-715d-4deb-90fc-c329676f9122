package database

import (
	"context"
	"fmt"
	"time"

	"github.com/legend-server/go-legend-server/internal/config"
	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

// Database 数据库管理器
type Database struct {
	db     *gorm.DB
	config *config.DatabaseConfig
	logger *zap.Logger
}

// NewDatabase 创建数据库管理器
func NewDatabase(cfg *config.DatabaseConfig) *Database {
	return &Database{
		config: cfg,
		logger: logger.GetLogger(),
	}
}

// Connect 连接数据库
func (d *Database) Connect() error {
	dsn := d.config.GetDSN()
	
	// 配置GORM日志
	gormConfig := &gorm.Config{
		Logger: gormlogger.New(
			&gormLogWriter{logger: d.logger},
			gormlogger.Config{
				SlowThreshold:             time.Second,
				LogLevel:                  gormlogger.Info,
				IgnoreRecordNotFoundError: true,
				Colorful:                  false,
			},
		),
	}

	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxOpenConns(d.config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(d.config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(d.config.ConnMaxLifetime)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	d.db = db
	d.logger.Info("Database connected successfully",
		zap.String("host", d.config.Host),
		zap.Int("port", d.config.Port),
		zap.String("database", d.config.Database))

	return nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	if d.db == nil {
		return nil
	}

	sqlDB, err := d.db.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// GetDB 获取GORM数据库实例
func (d *Database) GetDB() *gorm.DB {
	return d.db
}

// AutoMigrate 自动迁移数据库表
func (d *Database) AutoMigrate(models ...interface{}) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}

	for _, model := range models {
		if err := d.db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate model %T: %w", model, err)
		}
	}

	d.logger.Info("Database migration completed",
		zap.Int("models", len(models)))

	return nil
}

// Transaction 执行事务
func (d *Database) Transaction(fn func(*gorm.DB) error) error {
	return d.db.Transaction(fn)
}

// Health 健康检查
func (d *Database) Health() error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}

	sqlDB, err := d.db.DB()
	if err != nil {
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	return sqlDB.PingContext(ctx)
}

// GetStats 获取数据库统计信息
func (d *Database) GetStats() map[string]interface{} {
	if d.db == nil {
		return map[string]interface{}{
			"connected": false,
		}
	}

	sqlDB, err := d.db.DB()
	if err != nil {
		return map[string]interface{}{
			"connected": false,
			"error":     err.Error(),
		}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"connected":        true,
		"open_connections": stats.OpenConnections,
		"in_use":          stats.InUse,
		"idle":            stats.Idle,
		"wait_count":      stats.WaitCount,
		"wait_duration":   stats.WaitDuration.String(),
		"max_idle_closed": stats.MaxIdleClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}
}

// gormLogWriter GORM日志写入器
type gormLogWriter struct {
	logger *zap.Logger
}

func (w *gormLogWriter) Printf(format string, args ...interface{}) {
	w.logger.Info(fmt.Sprintf(format, args...))
}

// Repository 基础仓储接口
type Repository interface {
	Create(ctx context.Context, entity interface{}) error
	Update(ctx context.Context, entity interface{}) error
	Delete(ctx context.Context, id interface{}) error
	FindByID(ctx context.Context, id interface{}, entity interface{}) error
	FindAll(ctx context.Context, entities interface{}) error
}

// BaseRepository 基础仓储实现
type BaseRepository struct {
	db *gorm.DB
}

// NewBaseRepository 创建基础仓储
func NewBaseRepository(db *gorm.DB) *BaseRepository {
	return &BaseRepository{db: db}
}

// Create 创建实体
func (r *BaseRepository) Create(ctx context.Context, entity interface{}) error {
	return r.db.WithContext(ctx).Create(entity).Error
}

// Update 更新实体
func (r *BaseRepository) Update(ctx context.Context, entity interface{}) error {
	return r.db.WithContext(ctx).Save(entity).Error
}

// Delete 删除实体
func (r *BaseRepository) Delete(ctx context.Context, entity interface{}, id interface{}) error {
	return r.db.WithContext(ctx).Delete(entity, id).Error
}

// FindByID 根据ID查找实体
func (r *BaseRepository) FindByID(ctx context.Context, id interface{}, entity interface{}) error {
	return r.db.WithContext(ctx).First(entity, id).Error
}

// FindAll 查找所有实体
func (r *BaseRepository) FindAll(ctx context.Context, entities interface{}) error {
	return r.db.WithContext(ctx).Find(entities).Error
}

// FindWhere 条件查询
func (r *BaseRepository) FindWhere(ctx context.Context, entities interface{}, query interface{}, args ...interface{}) error {
	return r.db.WithContext(ctx).Where(query, args...).Find(entities).Error
}

// Count 计数
func (r *BaseRepository) Count(ctx context.Context, model interface{}, count *int64) error {
	return r.db.WithContext(ctx).Model(model).Count(count).Error
}

// Paginate 分页查询
func (r *BaseRepository) Paginate(ctx context.Context, entities interface{}, offset, limit int) error {
	return r.db.WithContext(ctx).Offset(offset).Limit(limit).Find(entities).Error
}
