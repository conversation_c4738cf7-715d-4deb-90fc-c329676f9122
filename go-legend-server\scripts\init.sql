-- Go传奇服务器数据库初始化脚本
-- 创建数据库和基础表结构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `legend_server` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `legend_server`;

-- 玩家表
CREATE TABLE IF NOT EXISTS `players` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `account_id` varchar(64) NOT NULL COMMENT '账号ID',
    `char_name` varchar(32) NOT NULL COMMENT '角色名',
    `job` tinyint NOT NULL DEFAULT '0' COMMENT '职业(0:战士,1:法师,2:道士)',
    `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别(0:男,1:女)',
    `level` int NOT NULL DEFAULT '1' COMMENT '等级',
    `experience` bigint NOT NULL DEFAULT '0' COMMENT '经验值',
    
    -- 属性
    `hp` int NOT NULL DEFAULT '100' COMMENT '当前生命值',
    `max_hp` int NOT NULL DEFAULT '100' COMMENT '最大生命值',
    `mp` int NOT NULL DEFAULT '100' COMMENT '当前魔法值',
    `max_mp` int NOT NULL DEFAULT '100' COMMENT '最大魔法值',
    `attack` int NOT NULL DEFAULT '10' COMMENT '攻击力',
    `defense` int NOT NULL DEFAULT '5' COMMENT '防御力',
    `magic_attack` int NOT NULL DEFAULT '10' COMMENT '魔法攻击',
    `magic_defense` int NOT NULL DEFAULT '5' COMMENT '魔法防御',
    `accuracy` int NOT NULL DEFAULT '10' COMMENT '准确度',
    `agility` int NOT NULL DEFAULT '10' COMMENT '敏捷度',
    
    -- 货币
    `game_money` bigint NOT NULL DEFAULT '1000' COMMENT '游戏币',
    `game_money_bind` bigint NOT NULL DEFAULT '0' COMMENT '绑定游戏币',
    `vcoin` bigint NOT NULL DEFAULT '0' COMMENT 'V币',
    `vcoin_bind` bigint NOT NULL DEFAULT '0' COMMENT '绑定V币',
    
    -- 位置信息
    `map_id` varchar(32) NOT NULL DEFAULT 'xinshou' COMMENT '地图ID',
    `x` int NOT NULL DEFAULT '60' COMMENT 'X坐标',
    `y` int NOT NULL DEFAULT '112' COMMENT 'Y坐标',
    `direction` int NOT NULL DEFAULT '4' COMMENT '方向',
    
    -- 状态
    `state` tinyint NOT NULL DEFAULT '0' COMMENT '状态(0:离线,1:在线,2:游戏中,3:死亡)',
    `attack_mode` tinyint NOT NULL DEFAULT '100' COMMENT '攻击模式',
    `pk_value` int NOT NULL DEFAULT '0' COMMENT 'PK值',
    
    -- 时间信息
    `online_time` bigint NOT NULL DEFAULT '0' COMMENT '在线时间(秒)',
    `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
    `last_logout_at` timestamp NULL DEFAULT NULL COMMENT '最后登出时间',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_char_name` (`char_name`),
    KEY `idx_account_id` (`account_id`),
    KEY `idx_level` (`level`),
    KEY `idx_map_id` (`map_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家表';

-- 装备表
CREATE TABLE IF NOT EXISTS `equipments` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `item_id` int NOT NULL COMMENT '物品ID',
    `name` varchar(64) NOT NULL COMMENT '装备名称',
    `type` tinyint NOT NULL COMMENT '装备类型',
    `quality` tinyint NOT NULL DEFAULT '0' COMMENT '品质',
    `level` int NOT NULL DEFAULT '1' COMMENT '装备等级',
    
    -- 使用限制
    `required_level` int NOT NULL DEFAULT '1' COMMENT '需求等级',
    `required_job` tinyint NOT NULL DEFAULT '255' COMMENT '需求职业',
    `required_gender` tinyint NOT NULL DEFAULT '255' COMMENT '需求性别',
    
    -- 耐久度
    `durability` int NOT NULL DEFAULT '100' COMMENT '当前耐久',
    `max_durability` int NOT NULL DEFAULT '100' COMMENT '最大耐久',
    
    -- 基础属性
    `attack` int NOT NULL DEFAULT '0' COMMENT '攻击力',
    `defense` int NOT NULL DEFAULT '0' COMMENT '防御力',
    `magic_attack` int NOT NULL DEFAULT '0' COMMENT '魔法攻击',
    `magic_defense` int NOT NULL DEFAULT '0' COMMENT '魔法防御',
    `accuracy` int NOT NULL DEFAULT '0' COMMENT '准确度',
    `agility` int NOT NULL DEFAULT '0' COMMENT '敏捷度',
    
    -- 生命魔法加成
    `hp_bonus` int NOT NULL DEFAULT '0' COMMENT '生命值加成',
    `mp_bonus` int NOT NULL DEFAULT '0' COMMENT '魔法值加成',
    
    -- 特殊属性
    `critical_rate` int NOT NULL DEFAULT '0' COMMENT '暴击率',
    `critical_damage` int NOT NULL DEFAULT '0' COMMENT '暴击伤害',
    `attack_speed` int NOT NULL DEFAULT '0' COMMENT '攻击速度',
    `move_speed` int NOT NULL DEFAULT '0' COMMENT '移动速度',
    `life_steal` int NOT NULL DEFAULT '0' COMMENT '生命偷取',
    `mana_steal` int NOT NULL DEFAULT '0' COMMENT '魔法偷取',
    
    -- 抗性
    `fire_resistance` int NOT NULL DEFAULT '0' COMMENT '火抗',
    `ice_resistance` int NOT NULL DEFAULT '0' COMMENT '冰抗',
    `light_resistance` int NOT NULL DEFAULT '0' COMMENT '雷抗',
    `poison_resistance` int NOT NULL DEFAULT '0' COMMENT '毒抗',
    
    -- 强化信息
    `enhance_level` int NOT NULL DEFAULT '0' COMMENT '强化等级',
    
    -- 绑定信息
    `is_bound` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否绑定',
    `bind_time` timestamp NULL DEFAULT NULL COMMENT '绑定时间',
    `owner_id` bigint unsigned DEFAULT NULL COMMENT '拥有者ID',
    
    -- 时效信息
    `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
    
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (`id`),
    KEY `idx_item_id` (`item_id`),
    KEY `idx_owner_id` (`owner_id`),
    KEY `idx_type` (`type`),
    KEY `idx_quality` (`quality`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='装备表';

-- 玩家装备表(记录玩家装备的装备)
CREATE TABLE IF NOT EXISTS `player_equipments` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `player_id` bigint unsigned NOT NULL COMMENT '玩家ID',
    `equipment_id` bigint unsigned NOT NULL COMMENT '装备ID',
    `position` tinyint NOT NULL COMMENT '装备位置',
    `equipped_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '装备时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_player_position` (`player_id`, `position`),
    KEY `idx_equipment_id` (`equipment_id`),
    FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`equipment_id`) REFERENCES `equipments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家装备表';

-- 怪物表
CREATE TABLE IF NOT EXISTS `monsters` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `type_id` int NOT NULL COMMENT '怪物类型ID',
    `name` varchar(64) NOT NULL COMMENT '怪物名称',
    `type` tinyint NOT NULL DEFAULT '0' COMMENT '怪物类型(0:普通,1:精英,2:BOSS)',
    `level` int NOT NULL DEFAULT '1' COMMENT '等级',
    
    -- 属性
    `hp` int NOT NULL DEFAULT '100' COMMENT '生命值',
    `max_hp` int NOT NULL DEFAULT '100' COMMENT '最大生命值',
    `mp` int NOT NULL DEFAULT '50' COMMENT '魔法值',
    `max_mp` int NOT NULL DEFAULT '50' COMMENT '最大魔法值',
    `attack` int NOT NULL DEFAULT '10' COMMENT '攻击力',
    `defense` int NOT NULL DEFAULT '5' COMMENT '防御力',
    `magic_attack` int NOT NULL DEFAULT '5' COMMENT '魔法攻击',
    `magic_defense` int NOT NULL DEFAULT '5' COMMENT '魔法防御',
    `accuracy` int NOT NULL DEFAULT '10' COMMENT '准确度',
    `agility` int NOT NULL DEFAULT '8' COMMENT '敏捷度',
    
    -- 位置信息
    `map_id` varchar(32) NOT NULL COMMENT '地图ID',
    `x` int NOT NULL COMMENT 'X坐标',
    `y` int NOT NULL COMMENT 'Y坐标',
    `spawn_x` int NOT NULL COMMENT '出生点X',
    `spawn_y` int NOT NULL COMMENT '出生点Y',
    
    -- AI相关
    `patrol_range` int NOT NULL DEFAULT '5' COMMENT '巡逻范围',
    `attack_range` int NOT NULL DEFAULT '1' COMMENT '攻击范围',
    `chase_range` int NOT NULL DEFAULT '8' COMMENT '追击范围',
    
    -- 掉落
    `exp_reward` bigint NOT NULL DEFAULT '10' COMMENT '经验奖励',
    `drop_table_id` int NOT NULL DEFAULT '1' COMMENT '掉落表ID',
    
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (`id`),
    KEY `idx_type_id` (`type_id`),
    KEY `idx_map_id` (`map_id`),
    KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='怪物表';

-- 技能表
CREATE TABLE IF NOT EXISTS `skills` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(64) NOT NULL COMMENT '技能名称',
    `type` tinyint NOT NULL DEFAULT '0' COMMENT '技能类型',
    `required_job` tinyint NOT NULL DEFAULT '255' COMMENT '需求职业',
    `required_level` int NOT NULL DEFAULT '1' COMMENT '需求等级',
    `max_level` int NOT NULL DEFAULT '10' COMMENT '最大等级',
    `base_damage` int NOT NULL DEFAULT '0' COMMENT '基础伤害',
    `base_mp_cost` int NOT NULL DEFAULT '5' COMMENT '基础魔法消耗',
    `base_range` int NOT NULL DEFAULT '1' COMMENT '基础范围',
    `base_cooldown` int NOT NULL DEFAULT '1000' COMMENT '基础冷却时间(毫秒)',
    `description` text COMMENT '技能描述',
    
    PRIMARY KEY (`id`),
    KEY `idx_required_job` (`required_job`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能表';

-- 玩家技能表
CREATE TABLE IF NOT EXISTS `player_skills` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `player_id` bigint unsigned NOT NULL COMMENT '玩家ID',
    `skill_id` int unsigned NOT NULL COMMENT '技能ID',
    `level` int NOT NULL DEFAULT '1' COMMENT '技能等级',
    `experience` bigint NOT NULL DEFAULT '0' COMMENT '技能经验',
    `learned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '学习时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_player_skill` (`player_id`, `skill_id`),
    FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`skill_id`) REFERENCES `skills` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家技能表';

-- 掉落表
CREATE TABLE IF NOT EXISTS `drop_tables` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(64) NOT NULL COMMENT '掉落表名称',
    `min_level` int NOT NULL DEFAULT '1' COMMENT '最小等级',
    `max_level` int NOT NULL DEFAULT '100' COMMENT '最大等级',
    `description` text COMMENT '描述',
    
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='掉落表';

-- 掉落项目表
CREATE TABLE IF NOT EXISTS `drop_items` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `drop_table_id` int unsigned NOT NULL COMMENT '掉落表ID',
    `item_type` tinyint NOT NULL COMMENT '物品类型(1:装备,2:道具,3:金币)',
    `item_id` int NOT NULL DEFAULT '0' COMMENT '物品ID',
    `min_quantity` int NOT NULL DEFAULT '1' COMMENT '最小数量',
    `max_quantity` int NOT NULL DEFAULT '1' COMMENT '最大数量',
    `drop_rate` int NOT NULL DEFAULT '1000' COMMENT '掉落率(万分比)',
    `quality_weights` json DEFAULT NULL COMMENT '品质权重',
    
    PRIMARY KEY (`id`),
    KEY `idx_drop_table_id` (`drop_table_id`),
    FOREIGN KEY (`drop_table_id`) REFERENCES `drop_tables` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='掉落项目表';

-- 装备模板表
CREATE TABLE IF NOT EXISTS `equipment_templates` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(64) NOT NULL COMMENT '装备名称',
    `type` tinyint NOT NULL COMMENT '装备类型',
    `base_quality` tinyint NOT NULL DEFAULT '0' COMMENT '基础品质',
    `min_level` int NOT NULL DEFAULT '1' COMMENT '最小等级',
    `max_level` int NOT NULL DEFAULT '100' COMMENT '最大等级',
    `required_job` tinyint NOT NULL DEFAULT '255' COMMENT '职业要求',
    `required_gender` tinyint NOT NULL DEFAULT '255' COMMENT '性别要求',

    -- 基础属性
    `base_attack` int NOT NULL DEFAULT '0',
    `base_defense` int NOT NULL DEFAULT '0',
    `base_magic_attack` int NOT NULL DEFAULT '0',
    `base_magic_defense` int NOT NULL DEFAULT '0',
    `base_accuracy` int NOT NULL DEFAULT '0',
    `base_agility` int NOT NULL DEFAULT '0',

    -- 属性范围
    `attack_min` int NOT NULL DEFAULT '0',
    `attack_max` int NOT NULL DEFAULT '0',
    `defense_min` int NOT NULL DEFAULT '0',
    `defense_max` int NOT NULL DEFAULT '0',
    `accuracy_min` int NOT NULL DEFAULT '0',
    `accuracy_max` int NOT NULL DEFAULT '0',

    -- 强化信息
    `can_enhance` tinyint(1) NOT NULL DEFAULT '1',
    `max_enhance_level` int NOT NULL DEFAULT '10',
    `socket_count` int NOT NULL DEFAULT '0',

    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_level_range` (`min_level`, `max_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='装备模板表';

-- 地图表
CREATE TABLE IF NOT EXISTS `maps` (
    `id` varchar(32) NOT NULL,
    `name` varchar(64) NOT NULL COMMENT '地图名称',
    `width` int NOT NULL DEFAULT '1000' COMMENT '地图宽度',
    `height` int NOT NULL DEFAULT '1000' COMMENT '地图高度',
    `is_safe_zone` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否安全区',
    `allow_pk` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否允许PK',
    `min_level` int NOT NULL DEFAULT '1' COMMENT '最小进入等级',
    `max_level` int NOT NULL DEFAULT '100' COMMENT '最大进入等级',

    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地图表';

-- 传送点表
CREATE TABLE IF NOT EXISTS `teleports` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(64) NOT NULL COMMENT '传送点名称',
    `from_map_id` varchar(32) NOT NULL COMMENT '起始地图',
    `from_x` int NOT NULL COMMENT '起始X坐标',
    `from_y` int NOT NULL COMMENT '起始Y坐标',
    `to_map_id` varchar(32) NOT NULL COMMENT '目标地图',
    `to_x` int NOT NULL COMMENT '目标X坐标',
    `to_y` int NOT NULL COMMENT '目标Y坐标',
    `required_level` int NOT NULL DEFAULT '1' COMMENT '需求等级',
    `cost` int NOT NULL DEFAULT '0' COMMENT '传送费用',

    PRIMARY KEY (`id`),
    KEY `idx_from_map` (`from_map_id`),
    KEY `idx_to_map` (`to_map_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='传送点表';

-- ================================
-- 初始化基础数据
-- ================================

-- 插入默认地图
INSERT IGNORE INTO `maps` (`id`, `name`, `width`, `height`, `is_safe_zone`, `allow_pk`, `min_level`, `max_level`) VALUES
('xinshou', '新手村', 500, 500, 1, 0, 1, 10),
('biqi', '比奇城', 800, 800, 1, 0, 1, 100),
('mengzhong', '蒙中', 600, 600, 0, 1, 10, 30),
('chiyue', '赤月峡谷', 1000, 1000, 0, 1, 30, 50);

-- 插入基础技能
INSERT IGNORE INTO `skills` (`id`, `name`, `type`, `required_job`, `required_level`, `max_level`, `base_damage`, `base_mp_cost`, `base_range`, `base_cooldown`, `description`) VALUES
(1, '基础剑术', 0, 0, 1, 10, 0, 0, 1, 0, '战士基础攻击技能'),
(2, '攻杀剑术', 1, 0, 7, 10, 20, 3, 1, 1500, '战士单体攻击技能'),
(3, '半月弯刀', 1, 0, 15, 10, 15, 5, 2, 2000, '战士群体攻击技能'),
(4, '烈火剑法', 1, 0, 25, 10, 50, 8, 1, 3000, '战士强力单体攻击'),
(5, '小火球', 1, 1, 1, 10, 15, 3, 3, 1000, '法师基础攻击魔法'),
(6, '大火球', 1, 1, 7, 10, 25, 5, 4, 1500, '法师中级火系魔法'),
(7, '雷电术', 1, 1, 12, 10, 30, 6, 5, 1200, '法师雷系攻击魔法'),
(8, '疾光电影', 1, 1, 20, 10, 40, 8, 6, 2000, '法师高级雷系魔法'),
(9, '治愈术', 2, 2, 1, 10, 0, 3, 1, 1000, '道士治疗技能'),
(10, '精神力战法', 2, 2, 7, 10, 0, 5, 0, 0, '道士增强精神力'),
(11, '召唤骷髅', 3, 2, 15, 10, 0, 10, 1, 5000, '道士召唤骷髅'),
(12, '隐身术', 4, 2, 20, 10, 0, 8, 0, 30000, '道士隐身技能');

-- 插入掉落表
INSERT IGNORE INTO `drop_tables` (`id`, `name`, `min_level`, `max_level`, `description`) VALUES
(1, '低级怪物掉落', 1, 10, '1-10级怪物掉落表'),
(2, '中级怪物掉落', 11, 25, '11-25级怪物掉落表'),
(3, 'BOSS掉落', 1, 100, 'BOSS专用掉落表');

-- 插入掉落项目
INSERT IGNORE INTO `drop_items` (`drop_table_id`, `item_type`, `item_id`, `min_quantity`, `max_quantity`, `drop_rate`, `quality_weights`) VALUES
-- 低级怪物掉落
(1, 3, 0, 10, 50, 8000, NULL), -- 金币 80%
(1, 2, 1001, 1, 1, 1500, NULL), -- 红药 15%
(1, 1, 1001, 1, 1, 500, '{"0": 70, "1": 25, "2": 5}'), -- 新手剑 5%

-- 中级怪物掉落
(2, 3, 0, 50, 150, 7000, NULL), -- 金币 70%
(2, 2, 1001, 1, 3, 2000, NULL), -- 红药 20%
(2, 1, 1002, 1, 1, 800, '{"1": 60, "2": 30, "3": 10}'), -- 精钢剑 8%
(2, 1, 2001, 1, 1, 200, '{"1": 50, "2": 35, "3": 15}'), -- 布衣 2%

-- BOSS掉落
(3, 3, 0, 500, 2000, 9000, NULL), -- 金币 90%
(3, 1, 1003, 1, 1, 500, '{"2": 40, "3": 35, "4": 20, "5": 5}'), -- 高级武器 5%
(3, 1, 2002, 1, 1, 300, '{"2": 40, "3": 35, "4": 20, "5": 5}'), -- 高级防具 3%
(3, 2, 2001, 5, 10, 2000, NULL); -- 高级药品 20%

-- 插入装备模板
INSERT IGNORE INTO `equipment_templates` (`id`, `name`, `type`, `base_quality`, `min_level`, `max_level`, `required_job`, `required_gender`,
    `base_attack`, `base_defense`, `base_magic_attack`, `base_magic_defense`, `base_accuracy`, `base_agility`,
    `attack_min`, `attack_max`, `defense_min`, `defense_max`, `accuracy_min`, `accuracy_max`,
    `can_enhance`, `max_enhance_level`, `socket_count`) VALUES

-- 武器模板
(1001, '新手剑', 0, 0, 1, 10, 0, 255, 15, 0, 0, 0, 5, 0, 10, 20, 0, 0, 3, 8, 1, 10, 1),
(1002, '精钢剑', 0, 1, 10, 20, 0, 255, 25, 0, 0, 0, 8, 0, 20, 35, 0, 0, 5, 12, 1, 15, 2),
(1003, '裁决之杖', 0, 3, 30, 50, 0, 255, 50, 0, 0, 0, 15, 0, 40, 65, 0, 0, 10, 20, 1, 20, 3),

-- 法师武器
(1101, '新手法杖', 0, 0, 1, 10, 1, 255, 0, 0, 12, 0, 3, 0, 0, 0, 0, 0, 2, 5, 1, 10, 1),
(1102, '骨玉权杖', 0, 1, 10, 20, 1, 255, 0, 0, 20, 0, 5, 0, 0, 0, 0, 0, 3, 8, 1, 15, 2),
(1103, '龙纹剑', 0, 3, 30, 50, 1, 255, 0, 0, 40, 0, 12, 0, 0, 0, 0, 0, 8, 15, 1, 20, 3),

-- 道士武器
(1201, '新手刀', 0, 0, 1, 10, 2, 255, 8, 0, 8, 0, 4, 0, 5, 12, 0, 0, 2, 6, 1, 10, 1),
(1202, '井中月', 0, 1, 10, 20, 2, 255, 15, 0, 15, 0, 7, 0, 12, 22, 0, 0, 4, 10, 1, 15, 2),
(1203, '怒斩', 0, 3, 30, 50, 2, 255, 30, 0, 30, 0, 15, 0, 25, 40, 0, 0, 10, 18, 1, 20, 3),

-- 防具模板 - 头盔
(2001, '新手头盔', 1, 0, 1, 10, 255, 255, 0, 8, 0, 5, 0, 0, 0, 0, 5, 12, 0, 0, 1, 10, 1),
(2002, '钢盔', 1, 1, 10, 20, 255, 255, 0, 15, 0, 10, 0, 0, 0, 0, 10, 22, 0, 0, 1, 15, 1),
(2003, '天尊头盔', 1, 3, 30, 50, 255, 255, 0, 30, 0, 20, 0, 0, 0, 0, 25, 40, 0, 0, 1, 20, 2),

-- 防具模板 - 衣服
(2101, '布衣', 2, 0, 1, 10, 255, 255, 0, 12, 0, 8, 0, 0, 0, 0, 8, 18, 0, 0, 1, 10, 2),
(2102, '重甲', 2, 1, 10, 20, 255, 255, 0, 25, 0, 15, 0, 0, 0, 0, 18, 35, 0, 0, 1, 15, 2),
(2103, '天尊道袍', 2, 3, 30, 50, 255, 255, 0, 45, 0, 30, 0, 0, 0, 0, 35, 60, 0, 0, 1, 20, 3),

-- 防具模板 - 鞋子
(2201, '布鞋', 3, 0, 1, 10, 255, 255, 0, 3, 0, 2, 0, 2, 0, 0, 2, 5, 1, 3, 1, 10, 0),
(2202, '皮靴', 3, 1, 10, 20, 255, 255, 0, 6, 0, 4, 0, 4, 0, 0, 4, 10, 2, 6, 1, 15, 1),
(2203, '战神靴', 3, 3, 30, 50, 255, 255, 0, 12, 0, 8, 0, 8, 0, 0, 8, 18, 5, 12, 1, 20, 1);

-- 插入基础怪物模板
INSERT IGNORE INTO `monsters` (`type_id`, `name`, `type`, `level`, `hp`, `max_hp`, `mp`, `max_mp`, `attack`, `defense`,
    `magic_attack`, `magic_defense`, `accuracy`, `agility`, `map_id`, `x`, `y`, `spawn_x`, `spawn_y`,
    `patrol_range`, `attack_range`, `chase_range`, `exp_reward`, `drop_table_id`) VALUES

-- 新手村怪物
(1001, '鸡', 0, 1, 15, 15, 0, 0, 3, 1, 0, 1, 5, 8, 'xinshou', 100, 100, 100, 100, 3, 1, 5, 5, 1),
(1002, '鹿', 0, 2, 25, 25, 0, 0, 5, 2, 0, 2, 6, 10, 'xinshou', 150, 150, 150, 150, 4, 1, 6, 8, 1),
(1003, '稻草人', 0, 3, 40, 40, 0, 0, 8, 3, 0, 3, 8, 6, 'xinshou', 200, 200, 200, 200, 2, 1, 4, 12, 1),

-- 蒙中怪物
(2001, '森林雪人', 0, 12, 180, 180, 50, 50, 25, 15, 10, 12, 15, 12, 'mengzhong', 300, 300, 300, 300, 5, 1, 8, 60, 2),
(2002, '钳虫', 0, 15, 250, 250, 30, 30, 35, 20, 5, 15, 18, 15, 'mengzhong', 400, 400, 400, 400, 4, 1, 7, 80, 2),
(2003, '蛾子', 0, 18, 320, 320, 80, 80, 30, 18, 25, 20, 20, 18, 'mengzhong', 500, 500, 500, 500, 6, 2, 10, 100, 2),

-- BOSS怪物
(3001, '沃玛教主', 2, 35, 2500, 2500, 500, 500, 80, 50, 60, 40, 35, 25, 'chiyue', 500, 500, 500, 500, 10, 2, 15, 1000, 3),
(3002, '祖玛教主', 2, 45, 5000, 5000, 800, 800, 120, 80, 100, 60, 45, 30, 'chiyue', 600, 600, 600, 600, 8, 3, 12, 2000, 3);

-- 插入传送点
INSERT IGNORE INTO `teleports` (`name`, `from_map_id`, `from_x`, `from_y`, `to_map_id`, `to_x`, `to_y`, `required_level`, `cost`) VALUES
('新手村到比奇', 'xinshou', 60, 112, 'biqi', 330, 330, 7, 0),
('比奇到新手村', 'biqi', 330, 330, 'xinshou', 60, 112, 1, 0),
('比奇到蒙中', 'biqi', 400, 400, 'mengzhong', 100, 100, 10, 100),
('蒙中到比奇', 'mengzhong', 100, 100, 'biqi', 400, 400, 1, 100),
('蒙中到赤月', 'mengzhong', 500, 500, 'chiyue', 50, 50, 25, 500),
('赤月到蒙中', 'chiyue', 50, 50, 'mengzhong', 500, 500, 1, 500);

-- ================================
-- 创建索引优化
-- ================================

-- 玩家相关索引
CREATE INDEX IF NOT EXISTS `idx_players_state_map` ON `players` (`state`, `map_id`);
CREATE INDEX IF NOT EXISTS `idx_players_level_job` ON `players` (`level`, `job`);
CREATE INDEX IF NOT EXISTS `idx_players_pk_value` ON `players` (`pk_value`);

-- 装备相关索引
CREATE INDEX IF NOT EXISTS `idx_equipments_owner_type` ON `equipments` (`owner_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_equipments_level_job` ON `equipments` (`required_level`, `required_job`);

-- 怪物相关索引
CREATE INDEX IF NOT EXISTS `idx_monsters_map_level` ON `monsters` (`map_id`, `level`);
CREATE INDEX IF NOT EXISTS `idx_monsters_type_level` ON `monsters` (`type`, `level`);

-- 技能相关索引
CREATE INDEX IF NOT EXISTS `idx_player_skills_player_level` ON `player_skills` (`player_id`, `level`);

-- ================================
-- 创建视图
-- ================================

-- 玩家详细信息视图
CREATE OR REPLACE VIEW `v_player_details` AS
SELECT
    p.*,
    COUNT(ps.id) as skill_count,
    COUNT(pe.id) as equipment_count
FROM `players` p
LEFT JOIN `player_skills` ps ON p.id = ps.player_id
LEFT JOIN `player_equipments` pe ON p.id = pe.player_id
GROUP BY p.id;

-- 在线玩家视图
CREATE OR REPLACE VIEW `v_online_players` AS
SELECT
    id, char_name, job, level, map_id, x, y, state, pk_value
FROM `players`
WHERE `state` IN (1, 2); -- 在线或游戏中

-- 装备统计视图
CREATE OR REPLACE VIEW `v_equipment_stats` AS
SELECT
    type,
    quality,
    COUNT(*) as count,
    AVG(enhance_level) as avg_enhance_level
FROM `equipments`
GROUP BY type, quality;

-- ================================
-- 存储过程
-- ================================

DELIMITER //

-- 创建角色存储过程
CREATE PROCEDURE IF NOT EXISTS `sp_create_character`(
    IN p_account_id VARCHAR(64),
    IN p_char_name VARCHAR(32),
    IN p_job TINYINT,
    IN p_gender TINYINT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 检查角色名是否已存在
    IF EXISTS(SELECT 1 FROM players WHERE char_name = p_char_name) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Character name already exists';
    END IF;

    -- 创建角色
    INSERT INTO players (account_id, char_name, job, gender)
    VALUES (p_account_id, p_char_name, p_job, p_gender);

    -- 给新角色学习基础技能
    SET @player_id = LAST_INSERT_ID();

    IF p_job = 0 THEN -- 战士
        INSERT INTO player_skills (player_id, skill_id) VALUES (@player_id, 1);
    ELSEIF p_job = 1 THEN -- 法师
        INSERT INTO player_skills (player_id, skill_id) VALUES (@player_id, 5);
    ELSEIF p_job = 2 THEN -- 道士
        INSERT INTO player_skills (player_id, skill_id) VALUES (@player_id, 9);
    END IF;

    COMMIT;

    SELECT @player_id as player_id;
END //

DELIMITER ;

-- ================================
-- 数据库配置优化
-- ================================

-- 设置字符集
SET NAMES utf8mb4;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_results = utf8mb4;

-- 完成初始化
SELECT 'Database initialization completed successfully!' as message;
