package main

import (
	"fmt"
	"time"

	"github.com/legend-server/go-legend-server/internal/game"
	"github.com/legend-server/go-legend-server/pkg/config"
	"github.com/legend-server/go-legend-server/pkg/logger"
)

func main() {
	// 初始化日志
	cfg := &config.Config{
		Log: config.LogConfig{
			Level:  "info",
			Format: "console",
		},
	}
	logger.Initialize(cfg)

	fmt.Println("=== 传奇游戏掉落系统演示 ===")

	// 创建掉落系统
	dropSystem := game.NewDropSystem()
	combatSystem := game.NewCombatSystem()

	// 创建测试玩家
	warrior := createTestWarrior()
	wizard := createTestWizard()

	fmt.Println("\n=== 玩家信息 ===")
	printPlayerInfo(warrior)
	printPlayerInfo(wizard)

	// 创建不同类型的怪物
	monsters := createTestMonsters(dropSystem)

	fmt.Println("\n=== 怪物信息 ===")
	for _, monster := range monsters {
		printMonsterInfo(monster)
	}

	fmt.Println("\n=== 战斗和掉落演示 ===")

	// 演示不同玩家击杀不同怪物的掉落
	for i, monster := range monsters {
		var attacker *game.Player
		if i%2 == 0 {
			attacker = warrior
		} else {
			attacker = wizard
		}

		fmt.Printf("\n--- %s 攻击 %s ---\n", attacker.CharName, monster.Name)

		// 模拟战斗
		battleResult := simulateBattle(combatSystem, attacker, monster)

		if battleResult {
			fmt.Printf("✅ %s 击败了 %s！\n", attacker.CharName, monster.Name)

			// 显示掉落物品
			showDroppedItems(dropSystem)

			// 演示拾取物品
			demonstrateItemPickup(dropSystem, attacker)
		} else {
			fmt.Printf("❌ %s 未能击败 %s\n", attacker.CharName, monster.Name)
		}
	}

	fmt.Println("\n=== 装备生成演示 ===")
	demonstrateEquipmentGeneration(dropSystem)

	fmt.Println("\n=== 掉落概率演示 ===")
	demonstrateDropRates(dropSystem)

	fmt.Println("\n=== 物品过期演示 ===")
	demonstrateItemExpiration(dropSystem)

	fmt.Println("\n掉落系统演示完成！")
}

func createTestWarrior() *game.Player {
	warrior := game.NewPlayer("warrior_account", "勇敢战士", game.JobWarrior, game.GenderMale)
	warrior.Level = 20
	warrior.Attack = 80
	warrior.Defense = 60
	warrior.HP = 300
	warrior.MaxHP = 300
	return warrior
}

func createTestWizard() *game.Player {
	wizard := game.NewPlayer("wizard_account", "智慧法师", game.JobWizard, game.GenderFemale)
	wizard.Level = 18
	wizard.MagicAttack = 90
	wizard.MagicDefense = 50
	wizard.HP = 180
	wizard.MaxHP = 180
	wizard.MP = 250
	wizard.MaxMP = 250
	return wizard
}

func createTestMonsters(dropSystem *game.DropSystem) []*game.Monster {
	monsters := []*game.Monster{
		// 低级怪物
		game.NewMonster(1001, "森林狼", 8, "forest", 100, 100),
		game.NewMonster(1002, "野猪", 12, "forest", 150, 150),

		// 中级怪物
		game.NewMonster(2001, "骷髅战士", 18, "dungeon", 200, 200),
		game.NewMonster(2002, "黑暗法师", 22, "dungeon", 250, 250),

		// Boss怪物
		game.NewMonster(3001, "森林之王", 25, "forest_boss", 300, 300),
	}

	// 设置怪物类型和掉落系统
	monsters[0].Type = game.MonsterTypeNormal
	monsters[1].Type = game.MonsterTypeNormal
	monsters[2].Type = game.MonsterTypeElite
	monsters[3].Type = game.MonsterTypeElite
	monsters[4].Type = game.MonsterTypeBoss

	for _, monster := range monsters {
		monster.SetDropSystem(dropSystem)
	}

	return monsters
}

func printPlayerInfo(player *game.Player) {
	fmt.Printf("玩家: %s (等级%d)\n", player.CharName, player.Level)
	fmt.Printf("  职业: %d, 性别: %d\n", player.Job, player.Gender)
	fmt.Printf("  生命值: %d/%d\n", player.HP, player.MaxHP)
	if player.Job == game.JobWizard {
		fmt.Printf("  魔法值: %d/%d\n", player.MP, player.MaxMP)
		fmt.Printf("  魔法攻击: %d, 魔法防御: %d\n", player.MagicAttack, player.MagicDefense)
	} else {
		fmt.Printf("  攻击力: %d, 防御力: %d\n", player.Attack, player.Defense)
	}
}

func printMonsterInfo(monster *game.Monster) {
	typeStr := "普通"
	if monster.Type == game.MonsterTypeElite {
		typeStr = "精英"
	} else if monster.Type == game.MonsterTypeBoss {
		typeStr = "Boss"
	}

	fmt.Printf("怪物: %s (等级%d, %s)\n", monster.Name, monster.Level, typeStr)
	fmt.Printf("  生命值: %d/%d\n", monster.HP, monster.MaxHP)
	fmt.Printf("  攻击力: %d, 防御力: %d\n", monster.Attack, monster.Defense)
	fmt.Printf("  经验奖励: %d, 掉落表ID: %d\n", monster.ExpReward, monster.DropTableID)
}

func simulateBattle(combatSystem *game.CombatSystem, attacker *game.Player, monster *game.Monster) bool {
	rounds := 0
	maxRounds := 20 // 防止无限循环

	for monster.IsAlive() && attacker.IsAlive() && rounds < maxRounds {
		rounds++

		// 玩家攻击怪物
		result, err := combatSystem.ProcessAttackMonster(attacker, monster, 0)
		if err != nil {
			fmt.Printf("攻击失败: %v\n", err)
			return false
		}

		if !result.IsMiss {
			fmt.Printf("第%d回合: %s 对 %s 造成 %d 点伤害",
				rounds, attacker.CharName, monster.Name, result.Damage)
			if result.IsDead {
				fmt.Println(" (致命一击!)")
				return true
			}
			fmt.Printf(" (怪物剩余生命值: %d)\n", monster.HP)
		} else {
			fmt.Printf("第%d回合: %s 攻击 %s 失手了\n",
				rounds, attacker.CharName, monster.Name)
		}

		// 简单的怪物反击（如果还活着）
		if monster.IsAlive() {
			damage := monster.Attack - attacker.Defense/2
			if damage < 1 {
				damage = 1
			}
			attacker.TakeDamage(damage)
			fmt.Printf("  %s 反击造成 %d 点伤害 (玩家剩余生命值: %d)\n",
				monster.Name, damage, attacker.HP)
		}
	}

	return !monster.IsAlive()
}

func showDroppedItems(dropSystem *game.DropSystem) {
	items := getAllDroppedItems(dropSystem)

	if len(items) == 0 {
		fmt.Println("💰 没有物品掉落")
		return
	}

	fmt.Printf("💰 掉落了 %d 个物品:\n", len(items))
	for i, item := range items {
		switch item.Type {
		case game.DropTypeEquipment:
			if item.Equipment != nil {
				fmt.Printf("  %d. 装备: %s (品质: %d, 强化: +%d)\n",
					i+1, item.Equipment.Name, item.Equipment.Quality, item.Equipment.EnhanceLevel)
			}
		case game.DropTypeGold:
			fmt.Printf("  %d. 金币: %d\n", i+1, item.Quantity)
		case game.DropTypeExperience:
			fmt.Printf("  %d. 经验: %d\n", i+1, item.Quantity)
		default:
			fmt.Printf("  %d. 物品ID: %d, 数量: %d\n", i+1, item.ItemID, item.Quantity)
		}
	}
}

func demonstrateItemPickup(dropSystem *game.DropSystem, player *game.Player) {
	items := getAllDroppedItems(dropSystem)

	if len(items) == 0 {
		return
	}

	// 拾取第一个物品
	item := items[0]
	pickedItem, err := dropSystem.PickupItem(item.ID, player.ID)
	if err != nil {
		fmt.Printf("❌ 拾取失败: %v\n", err)
		return
	}

	fmt.Printf("✅ %s 拾取了物品: ", player.CharName)
	if pickedItem.Type == game.DropTypeEquipment && pickedItem.Equipment != nil {
		fmt.Printf("装备 %s\n", pickedItem.Equipment.Name)
	} else {
		fmt.Printf("物品ID %d (数量: %d)\n", pickedItem.ItemID, pickedItem.Quantity)
	}
}

func demonstrateEquipmentGeneration(dropSystem *game.DropSystem) {
	fmt.Println("生成不同等级的装备:")

	templateIDs := []int32{1001, 1002, 2001, 2002}
	templateNames := []string{"新手剑", "精钢剑", "新手法杖", "魔法法杖"}
	levels := []int32{5, 15, 10, 20}

	for i, templateID := range templateIDs {
		equipment, err := dropSystem.GenerateEquipment(templateID, levels[i])
		if err != nil {
			fmt.Printf("❌ 生成装备失败: %v\n", err)
			continue
		}

		fmt.Printf("  %s: 品质%d, 攻击%d, 魔攻%d, 强化+%d\n",
			templateNames[i], equipment.Quality, equipment.Attack,
			equipment.MagicAttack, equipment.EnhanceLevel)
	}
}

func demonstrateDropRates(dropSystem *game.DropSystem) {
	fmt.Println("测试掉落概率 (进行100次掉落测试):")

	dropCounts := make(map[game.DropType]int)
	totalDrops := 0

	for i := 0; i < 100; i++ {
		items := dropSystem.ProcessDrop(1, 10, game.JobWarrior, "test", 0, 0)
		for _, item := range items {
			dropCounts[item.Type]++
			totalDrops++
		}
	}

	fmt.Printf("  总掉落次数: %d\n", totalDrops)
	for dropType, count := range dropCounts {
		typeName := "未知"
		switch dropType {
		case game.DropTypeEquipment:
			typeName = "装备"
		case game.DropTypeGold:
			typeName = "金币"
		case game.DropTypeExperience:
			typeName = "经验"
		}
		fmt.Printf("  %s: %d 次 (%.1f%%)\n", typeName, count, float64(count)/100.0*100)
	}
}

func demonstrateItemExpiration(dropSystem *game.DropSystem) {
	// 创建一个掉落物品
	items := dropSystem.ProcessDrop(1, 10, game.JobWarrior, "test", 0, 0)
	if len(items) == 0 {
		fmt.Println("没有物品可用于过期演示")
		return
	}

	item := items[0]
	fmt.Printf("创建了物品 %d，过期时间: %s\n", item.ID, item.ExpireTime.Format("15:04:05"))

	// 手动设置为过期
	item.ExpireTime = time.Now().Add(-1 * time.Second)
	fmt.Println("手动设置物品为过期状态")

	// 尝试拾取过期物品
	_, err := dropSystem.PickupItem(item.ID, 12345)
	if err != nil {
		fmt.Printf("✅ 正确拒绝了过期物品的拾取: %v\n", err)
	} else {
		fmt.Println("❌ 错误地允许了过期物品的拾取")
	}

	// 清理过期物品
	dropSystem.CleanupExpiredItems()
	fmt.Println("✅ 清理了过期物品")
}

// 辅助函数：获取所有掉落物品
func getAllDroppedItems(dropSystem *game.DropSystem) []*game.DroppedItem {
	return dropSystem.GetAllDroppedItems()
}
