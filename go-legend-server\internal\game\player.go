package game

import (
	"context"
	"sync"
	"time"

	"github.com/legend-server/go-legend-server/internal/database"
	"github.com/legend-server/go-legend-server/internal/network"
	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// JobType 职业类型
type JobType int32

const (
	JobWarrior JobType = 100 // 战士
	JobWizard  JobType = 101 // 法师
	JobTaoist  JobType = 102 // 道士
)

// GenderType 性别类型
type GenderType int32

const (
	GenderMale   GenderType = 200 // 男性
	GenderFemale GenderType = 201 // 女性
)

// AttackMode 攻击模式
type AttackMode int32

const (
	AttackModeAll   AttackMode = 100 // 全体攻击
	AttackModePeace AttackMode = 101 // 和平模式
	AttackModeGroup AttackMode = 102 // 组队模式
	AttackModeGuild AttackMode = 103 // 公会模式
	AttackModePK    AttackMode = 104 // PK模式
	AttackModeTeam  AttackMode = 105 // 队伍模式
)

// PlayerState 玩家状态
type PlayerState int32

const (
	PlayerStateOffline PlayerState = iota
	PlayerStateOnline
	PlayerStateInGame
	PlayerStateDead
)

// Player 玩家对象
type Player struct {
	// 基础信息
	ID          uint64     `json:"id" gorm:"primaryKey"`
	AccountID   string     `json:"account_id" gorm:"index"`
	CharName    string     `json:"char_name" gorm:"uniqueIndex"`
	Job         JobType    `json:"job"`
	Gender      GenderType `json:"gender"`
	Level       int32      `json:"level"`
	Experience  int64      `json:"experience"`
	
	// 属性
	HP          int32 `json:"hp"`
	MaxHP       int32 `json:"max_hp"`
	MP          int32 `json:"mp"`
	MaxMP       int32 `json:"max_mp"`
	Attack      int32 `json:"attack"`
	Defense     int32 `json:"defense"`
	MagicAttack int32 `json:"magic_attack"`
	MagicDefense int32 `json:"magic_defense"`
	Accuracy    int32 `json:"accuracy"`
	Agility     int32 `json:"agility"`
	
	// 货币
	GameMoney     int64 `json:"game_money"`
	GameMoneyBind int64 `json:"game_money_bind"`
	VCoin         int64 `json:"vcoin"`
	VCoinBind     int64 `json:"vcoin_bind"`
	
	// 位置信息
	MapID     string `json:"map_id"`
	X         int32  `json:"x"`
	Y         int32  `json:"y"`
	Direction int32  `json:"direction"`
	
	// 状态
	State      PlayerState `json:"state"`
	AttackMode AttackMode  `json:"attack_mode"`
	PKValue    int32       `json:"pk_value"`
	
	// 时间信息
	OnlineTime   int64     `json:"online_time"`
	LastLoginAt  time.Time `json:"last_login_at"`
	LastLogoutAt time.Time `json:"last_logout_at"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	
	// 运行时数据（不存储到数据库）
	session    *network.Session `json:"-" gorm:"-"`
	gameWorld  *GameWorld       `json:"-" gorm:"-"`
	mutex      sync.RWMutex     `json:"-" gorm:"-"`
	logger     *zap.Logger      `json:"-" gorm:"-"`
	lastUpdate time.Time        `json:"-" gorm:"-"`
	
	// 子系统
	inventory *Inventory `json:"-" gorm:"-"`
	skills    *SkillSet  `json:"-" gorm:"-"`
	buffs     *BuffSet   `json:"-" gorm:"-"`
}

// NewPlayer 创建新玩家
func NewPlayer(accountID, charName string, job JobType, gender GenderType) *Player {
	now := time.Now()
	player := &Player{
		AccountID:     accountID,
		CharName:      charName,
		Job:           job,
		Gender:        gender,
		Level:         1,
		Experience:    0,
		HP:            100,
		MaxHP:         100,
		MP:            100,
		MaxMP:         100,
		Attack:        10,
		Defense:       5,
		MagicAttack:   10,
		MagicDefense:  5,
		Accuracy:      10,
		Agility:       10,
		GameMoney:     1000,
		MapID:         "xinshou",
		X:             60,
		Y:             112,
		Direction:     4,
		State:         PlayerStateOffline,
		AttackMode:    AttackModePeace,
		CreatedAt:     now,
		UpdatedAt:     now,
		logger:        logger.GetLogger(),
		lastUpdate:    now,
	}
	
	// 初始化子系统
	player.inventory = NewInventory(player)
	player.skills = NewSkillSet(player)
	player.buffs = NewBuffSet(player)
	
	return player
}

// SetSession 设置网络会话
func (p *Player) SetSession(session *network.Session) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.session = session
}

// GetSession 获取网络会话
func (p *Player) GetSession() *network.Session {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.session
}

// SetGameWorld 设置游戏世界
func (p *Player) SetGameWorld(world *GameWorld) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.gameWorld = world
}

// GetGameWorld 获取游戏世界
func (p *Player) GetGameWorld() *GameWorld {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.gameWorld
}

// EnterGame 进入游戏
func (p *Player) EnterGame() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	p.State = PlayerStateInGame
	p.LastLoginAt = time.Now()
	
	p.logger.Info("Player entered game",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName))
	
	return nil
}

// ExitGame 退出游戏
func (p *Player) ExitGame() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	p.State = PlayerStateOffline
	p.LastLogoutAt = time.Now()
	
	p.logger.Info("Player exited game",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName))
	
	return nil
}

// Update 更新玩家状态
func (p *Player) Update(deltaTime time.Duration) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	now := time.Now()
	
	// 更新在线时间
	if p.State == PlayerStateInGame {
		p.OnlineTime += int64(deltaTime.Seconds())
	}
	
	// 更新子系统
	if p.buffs != nil {
		p.buffs.Update(deltaTime)
	}
	
	// 自动回血回蓝
	p.autoRecover(deltaTime)
	
	p.lastUpdate = now
}

// autoRecover 自动回复HP/MP
func (p *Player) autoRecover(deltaTime time.Duration) {
	if p.State != PlayerStateInGame || p.State == PlayerStateDead {
		return
	}
	
	// 每5秒回复一次
	if time.Since(p.lastUpdate) >= 5*time.Second {
		// 回血
		if p.HP < p.MaxHP {
			recover := p.MaxHP / 20 // 5%回复
			if recover < 1 {
				recover = 1
			}
			p.HP += recover
			if p.HP > p.MaxHP {
				p.HP = p.MaxHP
			}
		}
		
		// 回蓝
		if p.MP < p.MaxMP {
			recover := p.MaxMP / 20 // 5%回复
			if recover < 1 {
				recover = 1
			}
			p.MP += recover
			if p.MP > p.MaxMP {
				p.MP = p.MaxMP
			}
		}
	}
}

// MoveTo 移动到指定位置
func (p *Player) MoveTo(x, y int32, direction int32) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	// TODO: 验证移动合法性
	
	p.X = x
	p.Y = y
	p.Direction = direction
	
	return nil
}

// ChangeMap 切换地图
func (p *Player) ChangeMap(mapID string, x, y int32) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	oldMapID := p.MapID
	p.MapID = mapID
	p.X = x
	p.Y = y
	
	p.logger.Info("Player changed map",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName),
		zap.String("old_map", oldMapID),
		zap.String("new_map", mapID))
	
	return nil
}

// AddExperience 增加经验
func (p *Player) AddExperience(exp int64) bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	p.Experience += exp
	
	// 检查升级
	return p.checkLevelUp()
}

// checkLevelUp 检查升级
func (p *Player) checkLevelUp() bool {
	// TODO: 实现升级逻辑
	return false
}

// TakeDamage 受到伤害
func (p *Player) TakeDamage(damage int32) bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	p.HP -= damage
	if p.HP <= 0 {
		p.HP = 0
		p.State = PlayerStateDead
		return true // 死亡
	}
	
	return false
}

// Heal 治疗
func (p *Player) Heal(amount int32) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	p.HP += amount
	if p.HP > p.MaxHP {
		p.HP = p.MaxHP
	}
}

// IsAlive 是否存活
func (p *Player) IsAlive() bool {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.State != PlayerStateDead
}

// GetPosition 获取位置
func (p *Player) GetPosition() (string, int32, int32) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.MapID, p.X, p.Y
}

// Save 保存玩家数据
func (p *Player) Save(ctx context.Context, db *database.Database) error {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	
	p.UpdatedAt = time.Now()
	return db.GetDB().WithContext(ctx).Save(p).Error
}
