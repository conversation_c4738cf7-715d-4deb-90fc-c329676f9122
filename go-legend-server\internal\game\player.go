package game

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/legend-server/go-legend-server/internal/database"
	"github.com/legend-server/go-legend-server/internal/network"
	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// JobType 职业类型
type JobType int32

const (
	JobWarrior JobType = 100 // 战士
	JobWizard  JobType = 101 // 法师
	JobTaoist  JobType = 102 // 道士
)

// GenderType 性别类型
type GenderType int32

const (
	GenderMale   GenderType = 200 // 男性
	GenderFemale GenderType = 201 // 女性
)

// AttackMode 攻击模式
type AttackMode int32

const (
	AttackModeAll   AttackMode = 100 // 全体攻击
	AttackModePeace AttackMode = 101 // 和平模式
	AttackModeGroup AttackMode = 102 // 组队模式
	AttackModeGuild AttackMode = 103 // 公会模式
	AttackModePK    AttackMode = 104 // PK模式
	AttackModeTeam  AttackMode = 105 // 队伍模式
)

// PlayerState 玩家状态
type PlayerState int32

const (
	PlayerStateOffline PlayerState = iota
	PlayerStateOnline
	PlayerStateInGame
	PlayerStateDead
)

// Player 玩家对象
type Player struct {
	// 基础信息
	ID         uint64     `json:"id" gorm:"primaryKey"`
	AccountID  string     `json:"account_id" gorm:"index"`
	CharName   string     `json:"char_name" gorm:"uniqueIndex"`
	Job        JobType    `json:"job"`
	Gender     GenderType `json:"gender"`
	Level      int32      `json:"level"`
	Experience int64      `json:"experience"`

	// 属性
	HP           int32 `json:"hp"`
	MaxHP        int32 `json:"max_hp"`
	MP           int32 `json:"mp"`
	MaxMP        int32 `json:"max_mp"`
	Attack       int32 `json:"attack"`
	Defense      int32 `json:"defense"`
	MagicAttack  int32 `json:"magic_attack"`
	MagicDefense int32 `json:"magic_defense"`
	Accuracy     int32 `json:"accuracy"`
	Agility      int32 `json:"agility"`

	// 货币
	GameMoney     int64 `json:"game_money"`
	GameMoneyBind int64 `json:"game_money_bind"`
	VCoin         int64 `json:"vcoin"`
	VCoinBind     int64 `json:"vcoin_bind"`

	// 位置信息
	MapID     string `json:"map_id"`
	X         int32  `json:"x"`
	Y         int32  `json:"y"`
	Direction int32  `json:"direction"`

	// 状态
	State      PlayerState `json:"state"`
	AttackMode AttackMode  `json:"attack_mode"`
	PKValue    int32       `json:"pk_value"`

	// 时间信息
	OnlineTime   int64     `json:"online_time"`
	LastLoginAt  time.Time `json:"last_login_at"`
	LastLogoutAt time.Time `json:"last_logout_at"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// 运行时数据（不存储到数据库）
	session    *network.Session `json:"-" gorm:"-"`
	gameWorld  *GameWorld       `json:"-" gorm:"-"`
	mutex      sync.RWMutex     `json:"-" gorm:"-"`
	logger     *zap.Logger      `json:"-" gorm:"-"`
	lastUpdate time.Time        `json:"-" gorm:"-"`

	// 子系统
	inventory *Inventory `json:"-" gorm:"-"`
	skills    *SkillSet  `json:"-" gorm:"-"`
	buffs     *BuffSet   `json:"-" gorm:"-"`
}

// NewPlayer 创建新玩家
func NewPlayer(accountID, charName string, job JobType, gender GenderType) *Player {
	now := time.Now()
	player := &Player{
		AccountID:    accountID,
		CharName:     charName,
		Job:          job,
		Gender:       gender,
		Level:        1,
		Experience:   0,
		HP:           100,
		MaxHP:        100,
		MP:           100,
		MaxMP:        100,
		Attack:       10,
		Defense:      5,
		MagicAttack:  10,
		MagicDefense: 5,
		Accuracy:     10,
		Agility:      10,
		GameMoney:    1000,
		MapID:        "xinshou",
		X:            60,
		Y:            112,
		Direction:    4,
		State:        PlayerStateOffline,
		AttackMode:   AttackModePeace,
		CreatedAt:    now,
		UpdatedAt:    now,
		logger:       logger.GetLogger(),
		lastUpdate:   now,
	}

	// 初始化子系统
	player.inventory = NewInventory(player)
	player.skills = NewSkillSet(player)
	player.buffs = NewBuffSet(player)

	return player
}

// SetSession 设置网络会话
func (p *Player) SetSession(session *network.Session) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.session = session
}

// GetSession 获取网络会话
func (p *Player) GetSession() *network.Session {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.session
}

// SetGameWorld 设置游戏世界
func (p *Player) SetGameWorld(world *GameWorld) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.gameWorld = world
}

// SetInventory 设置背包系统
func (p *Player) SetInventory(inventory *Inventory) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.inventory = inventory
}

// SetSkills 设置技能系统
func (p *Player) SetSkills(skills *SkillSet) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.skills = skills
}

// SetBuffs 设置Buff系统
func (p *Player) SetBuffs(buffs *BuffSet) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.buffs = buffs
}

// GetSkills 获取技能系统
func (p *Player) GetSkills() *SkillSet {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.skills
}

// GetGameWorld 获取游戏世界
func (p *Player) GetGameWorld() *GameWorld {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.gameWorld
}

// EnterGame 进入游戏
func (p *Player) EnterGame() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.State = PlayerStateInGame
	p.LastLoginAt = time.Now()

	p.logger.Info("Player entered game",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName))

	return nil
}

// ExitGame 退出游戏
func (p *Player) ExitGame() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.State = PlayerStateOffline
	p.LastLogoutAt = time.Now()

	p.logger.Info("Player exited game",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName))

	return nil
}

// Update 更新玩家状态
func (p *Player) Update(deltaTime time.Duration) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	now := time.Now()

	// 更新在线时间
	if p.State == PlayerStateInGame {
		p.OnlineTime += int64(deltaTime.Seconds())
	}

	// 更新子系统
	if p.buffs != nil {
		p.buffs.Update(deltaTime)
	}

	// 自动回血回蓝
	p.autoRecover(deltaTime)

	p.lastUpdate = now
}

// autoRecover 自动回复HP/MP
func (p *Player) autoRecover(deltaTime time.Duration) {
	if p.State != PlayerStateInGame || p.State == PlayerStateDead {
		return
	}

	// 每5秒回复一次
	if time.Since(p.lastUpdate) >= 5*time.Second {
		// 回血
		if p.HP < p.MaxHP {
			recover := p.MaxHP / 20 // 5%回复
			if recover < 1 {
				recover = 1
			}
			p.HP += recover
			if p.HP > p.MaxHP {
				p.HP = p.MaxHP
			}
		}

		// 回蓝
		if p.MP < p.MaxMP {
			recover := p.MaxMP / 20 // 5%回复
			if recover < 1 {
				recover = 1
			}
			p.MP += recover
			if p.MP > p.MaxMP {
				p.MP = p.MaxMP
			}
		}
	}
}

// MoveTo 移动到指定位置
func (p *Player) MoveTo(x, y int32, direction int32) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// TODO: 验证移动合法性

	p.X = x
	p.Y = y
	p.Direction = direction

	return nil
}

// ChangeMap 切换地图
func (p *Player) ChangeMap(mapID string, x, y int32) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	oldMapID := p.MapID
	p.MapID = mapID
	p.X = x
	p.Y = y

	p.logger.Info("Player changed map",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName),
		zap.String("old_map", oldMapID),
		zap.String("new_map", mapID))

	return nil
}

// AddExperience 增加经验
func (p *Player) AddExperience(exp int64) bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.Experience += exp

	// 检查升级
	return p.checkLevelUp()
}

// checkLevelUp 检查升级
func (p *Player) checkLevelUp() bool {
	// 获取当前等级所需经验
	requiredExp := p.getRequiredExperience(p.Level)

	if p.Experience >= requiredExp {
		// 升级
		p.Level++
		p.Experience -= requiredExp

		// 增加属性
		p.addLevelUpAttributes()

		// 回满血蓝
		p.HP = p.MaxHP
		p.MP = p.MaxMP

		p.logger.Info("Player level up",
			zap.Uint64("player_id", p.ID),
			zap.String("char_name", p.CharName),
			zap.Int32("new_level", p.Level))

		// 检查是否还能继续升级
		return p.checkLevelUp()
	}

	return false
}

// getRequiredExperience 获取指定等级所需经验
func (p *Player) getRequiredExperience(level int32) int64 {
	// 简化的经验公式：level * level * 100
	return int64(level * level * 100)
}

// addLevelUpAttributes 升级时增加属性
func (p *Player) addLevelUpAttributes() {
	// 根据职业增加不同属性
	switch p.Job {
	case JobWarrior:
		p.MaxHP += 15
		p.MaxMP += 5
		p.Attack += 3
		p.Defense += 2
	case JobWizard:
		p.MaxHP += 8
		p.MaxMP += 12
		p.MagicAttack += 4
		p.MagicDefense += 2
	case JobTaoist:
		p.MaxHP += 10
		p.MaxMP += 10
		p.Attack += 2
		p.MagicAttack += 2
		p.Defense += 1
		p.MagicDefense += 1
	}

	// 所有职业都增加的属性
	p.Accuracy += 1
	p.Agility += 1
}

// TakeDamage 受到伤害
func (p *Player) TakeDamage(damage int32) bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.HP -= damage
	if p.HP <= 0 {
		p.HP = 0
		p.State = PlayerStateDead
		return true // 死亡
	}

	return false
}

// Heal 治疗
func (p *Player) Heal(amount int32) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.HP += amount
	if p.HP > p.MaxHP {
		p.HP = p.MaxHP
	}
}

// IsAlive 是否存活
func (p *Player) IsAlive() bool {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.State != PlayerStateDead
}

// GetPosition 获取位置
func (p *Player) GetPosition() (string, int32, int32) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.MapID, p.X, p.Y
}

// AttackTarget 攻击目标
func (p *Player) AttackTarget(target *Player, skillID int32) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.IsAlive() {
		return fmt.Errorf("player is dead")
	}

	if !target.IsAlive() {
		return fmt.Errorf("target is dead")
	}

	// 检查攻击距离
	if !p.isInAttackRange(target) {
		return fmt.Errorf("target out of range")
	}

	// 检查PK规则
	if !p.canAttackPlayer(target) {
		return fmt.Errorf("cannot attack this player")
	}

	// 计算伤害
	damage := p.calculateDamage(target, skillID)

	// 应用伤害
	isDead := target.TakeDamage(damage)

	// 增加PK值
	if target.PKValue < 400 && p.PKValue == 0 {
		p.AddPKValue(10)
	}

	p.logger.Info("Player attack",
		zap.Uint64("attacker_id", p.ID),
		zap.String("attacker_name", p.CharName),
		zap.Uint64("target_id", target.ID),
		zap.String("target_name", target.CharName),
		zap.Int32("damage", damage),
		zap.Bool("target_dead", isDead))

	return nil
}

// isInAttackRange 检查是否在攻击范围内
func (p *Player) isInAttackRange(target *Player) bool {
	if p.MapID != target.MapID {
		return false
	}

	dx := p.X - target.X
	dy := p.Y - target.Y
	distance := dx*dx + dy*dy

	// 攻击范围为3格
	return distance <= 9
}

// canAttackPlayer 检查是否可以攻击玩家
func (p *Player) canAttackPlayer(target *Player) bool {
	// 和平模式不能攻击
	if p.AttackMode == AttackModePeace {
		return false
	}

	// 等级限制
	if p.Level < 7 || target.Level < 7 {
		return false
	}

	// 同一账号不能攻击
	if p.AccountID == target.AccountID {
		return false
	}

	return true
}

// calculateDamage 计算伤害
func (p *Player) calculateDamage(target *Player, skillID int32) int32 {
	// 基础攻击力
	var baseDamage int32
	if skillID == 0 {
		// 普通攻击
		baseDamage = p.Attack + int32(rand.Intn(int(p.Attack/2)))
	} else {
		// 技能攻击
		skill, exists := p.skills.GetSkill(skillID)
		if !exists {
			baseDamage = p.Attack
		} else {
			baseDamage = p.Attack + skill.Damage
		}
	}

	// 防御减免
	defense := target.Defense
	if skillID > 0 {
		// 魔法攻击使用魔法防御
		defense = target.MagicDefense
	}

	// 伤害计算公式
	damage := baseDamage - defense/2
	if damage < 1 {
		damage = 1
	}

	// 随机浮动 ±20%
	variation := int32(float32(damage) * 0.2)
	damage += int32(rand.Intn(int(variation*2))) - variation

	if damage < 1 {
		damage = 1
	}

	return damage
}

// AddPKValue 增加PK值
func (p *Player) AddPKValue(value int32) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.PKValue += value
	if p.PKValue > 1000 {
		p.PKValue = 1000
	}

	p.logger.Debug("PK value changed",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName),
		zap.Int32("pk_value", p.PKValue))
}

// SubPKValue 减少PK值
func (p *Player) SubPKValue(value int32) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.PKValue -= value
	if p.PKValue < 0 {
		p.PKValue = 0
	}
}

// UseSkill 使用技能
func (p *Player) UseSkill(skillID int32, targetX, targetY int32, targetID uint64) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.IsAlive() {
		return fmt.Errorf("player is dead")
	}

	// 获取技能
	skill, exists := p.skills.GetSkill(skillID)
	if !exists {
		return fmt.Errorf("skill not found")
	}

	// 检查冷却时间
	if time.Since(skill.LastUseTime) < time.Duration(skill.Cooldown)*time.Millisecond {
		return fmt.Errorf("skill is in cooldown")
	}

	// 检查魔法值
	if p.MP < skill.MPCost {
		return fmt.Errorf("not enough MP")
	}

	// 消耗魔法值
	p.MP -= skill.MPCost
	if p.MP < 0 {
		p.MP = 0
	}

	// 更新技能使用时间
	skill.LastUseTime = time.Now()

	// 增加技能经验
	p.addSkillExperience(skillID, 1)

	p.logger.Info("Player used skill",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName),
		zap.Int32("skill_id", skillID),
		zap.Int32("target_x", targetX),
		zap.Int32("target_y", targetY),
		zap.Uint64("target_id", targetID))

	return nil
}

// addSkillExperience 增加技能经验
func (p *Player) addSkillExperience(skillID int32, exp int64) {
	skill, exists := p.skills.GetSkill(skillID)
	if !exists {
		return
	}

	skill.Experience += exp

	// 检查技能升级
	requiredExp := p.getSkillRequiredExperience(skill.Level)
	if skill.Experience >= requiredExp {
		skill.Level++
		skill.Experience -= requiredExp

		// 更新技能属性
		p.updateSkillAttributes(skill)

		p.logger.Info("Skill level up",
			zap.Uint64("player_id", p.ID),
			zap.String("char_name", p.CharName),
			zap.Int32("skill_id", skillID),
			zap.Int32("new_level", skill.Level))
	}
}

// getSkillRequiredExperience 获取技能升级所需经验
func (p *Player) getSkillRequiredExperience(level int32) int64 {
	return int64(level * level * 50)
}

// updateSkillAttributes 更新技能属性
func (p *Player) updateSkillAttributes(skill *Skill) {
	// 根据技能等级更新属性
	skill.Damage = skill.Level * 10
	skill.MPCost = 5 + skill.Level*2
	skill.Range = 3 + skill.Level/5
	skill.Cooldown = 1000 - skill.Level*10 // 冷却时间随等级减少
	if skill.Cooldown < 100 {
		skill.Cooldown = 100
	}
}

// LearnSkill 学习技能
func (p *Player) LearnSkill(skillID int32) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 检查是否已经学会
	if _, exists := p.skills.GetSkill(skillID); exists {
		return fmt.Errorf("skill already learned")
	}

	// 创建新技能
	skill := &Skill{
		ID:          skillID,
		Level:       1,
		Experience:  0,
		LastUseTime: time.Time{},
	}

	// 初始化技能属性
	p.updateSkillAttributes(skill)

	// 添加到技能集
	p.skills.AddSkill(skill)

	p.logger.Info("Player learned skill",
		zap.Uint64("player_id", p.ID),
		zap.String("char_name", p.CharName),
		zap.Int32("skill_id", skillID))

	return nil
}

// Save 保存玩家数据
func (p *Player) Save(ctx context.Context, db *database.Database) error {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	p.UpdatedAt = time.Now()
	return db.GetDB().WithContext(ctx).Save(p).Error
}
