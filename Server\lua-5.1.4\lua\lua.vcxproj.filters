﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx</Extensions>
    </Filter>
    <Filter Include="Library Files">
      <UniqueIdentifier>{860396f5-1d0b-4770-a22c-ed8891e99584}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\lapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lcode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ldebug.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ldo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ldump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lfunc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lgc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\llex.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lobject.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lopcodes.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lparser.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lstate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lstring.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ltable.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ltm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lundump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lvm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lzio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\noparser.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lauxlib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lbaselib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ldblib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\linit.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\liolib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lmathlib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\loadlib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\loslib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\lstrlib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ltablib.c">
      <Filter>Library Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\src\lapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lauxlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lcode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\ldebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\ldo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lfunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lgc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\llex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\llimits.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lmem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lobject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lopcodes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lparser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lstate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lstring.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\ltable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\ltm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lua.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\luaconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lualib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lundump.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lvm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\lzio.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>