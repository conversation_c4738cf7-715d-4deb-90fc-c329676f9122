<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="lua"
	ProjectGUID="{79C9F726-3AA3-4FA5-87DF-5AB8963DD97D}"
	Keyword="Win32Proj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/lua.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;NODUMP"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/lua.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="ReleaseL|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;NODUMP"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/lua.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath="..\src\lapi.c"
				>
			</File>
			<File
				RelativePath="..\src\lcode.c"
				>
				<FileConfiguration
					Name="Release|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="ReleaseL|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\ldebug.c"
				>
			</File>
			<File
				RelativePath="..\src\ldo.c"
				>
			</File>
			<File
				RelativePath="..\src\ldump.c"
				>
			</File>
			<File
				RelativePath="..\src\lfunc.c"
				>
			</File>
			<File
				RelativePath="..\src\lgc.c"
				>
			</File>
			<File
				RelativePath="..\src\llex.c"
				>
				<FileConfiguration
					Name="Release|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="ReleaseL|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\lmem.c"
				>
			</File>
			<File
				RelativePath="..\src\lobject.c"
				>
			</File>
			<File
				RelativePath="..\src\lopcodes.c"
				>
			</File>
			<File
				RelativePath="..\src\lparser.c"
				>
				<FileConfiguration
					Name="Release|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="ReleaseL|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\lstate.c"
				>
			</File>
			<File
				RelativePath="..\src\lstring.c"
				>
			</File>
			<File
				RelativePath="..\src\ltable.c"
				>
			</File>
			<File
				RelativePath="..\src\ltm.c"
				>
			</File>
			<File
				RelativePath="..\src\lundump.c"
				>
			</File>
			<File
				RelativePath="..\src\lvm.c"
				>
			</File>
			<File
				RelativePath="..\src\lzio.c"
				>
			</File>
			<File
				RelativePath="..\src\noparser.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="ReleaseL|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath="..\src\lapi.h"
				>
			</File>
			<File
				RelativePath="..\src\lauxlib.h"
				>
			</File>
			<File
				RelativePath="..\src\lcode.h"
				>
			</File>
			<File
				RelativePath="..\src\ldebug.h"
				>
			</File>
			<File
				RelativePath="..\src\ldo.h"
				>
			</File>
			<File
				RelativePath="..\src\lfunc.h"
				>
			</File>
			<File
				RelativePath="..\src\lgc.h"
				>
			</File>
			<File
				RelativePath="..\src\llex.h"
				>
			</File>
			<File
				RelativePath="..\src\llimits.h"
				>
			</File>
			<File
				RelativePath="..\src\lmem.h"
				>
			</File>
			<File
				RelativePath="..\src\lobject.h"
				>
			</File>
			<File
				RelativePath="..\src\lopcodes.h"
				>
			</File>
			<File
				RelativePath="..\src\lparser.h"
				>
			</File>
			<File
				RelativePath="..\src\lstate.h"
				>
			</File>
			<File
				RelativePath="..\src\lstring.h"
				>
			</File>
			<File
				RelativePath="..\src\ltable.h"
				>
			</File>
			<File
				RelativePath="..\src\ltm.h"
				>
			</File>
			<File
				RelativePath="..\src\lua.h"
				>
			</File>
			<File
				RelativePath="..\src\luaconf.h"
				>
			</File>
			<File
				RelativePath="..\src\lualib.h"
				>
			</File>
			<File
				RelativePath="..\src\lundump.h"
				>
			</File>
			<File
				RelativePath="..\src\lvm.h"
				>
			</File>
			<File
				RelativePath="..\src\lzio.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
		<Filter
			Name="Library Files"
			>
			<File
				RelativePath="..\src\lauxlib.c"
				>
			</File>
			<File
				RelativePath="..\src\lbaselib.c"
				>
			</File>
			<File
				RelativePath="..\src\ldblib.c"
				>
			</File>
			<File
				RelativePath="..\src\linit.c"
				>
			</File>
			<File
				RelativePath="..\src\liolib.c"
				>
			</File>
			<File
				RelativePath="..\src\lmathlib.c"
				>
			</File>
			<File
				RelativePath="..\src\loadlib.c"
				>
			</File>
			<File
				RelativePath="..\src\loslib.c"
				>
			</File>
			<File
				RelativePath="..\src\lstrlib.c"
				>
			</File>
			<File
				RelativePath="..\src\ltablib.c"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
