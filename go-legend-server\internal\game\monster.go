package game

import (
	"math"
	"math/rand"
	"sync"
	"time"

	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// MonsterType 怪物类型
type MonsterType int32

const (
	MonsterTypeNormal MonsterType = iota // 普通怪物
	MonsterTypeElite                     // 精英怪物
	MonsterTypeBoss                      // Boss怪物
)

// MonsterState 怪物状态
type MonsterState int32

const (
	MonsterStateIdle    MonsterState = iota // 空闲
	MonsterStatePatrol                      // 巡逻
	MonsterStateCombat                      // 战斗
	MonsterStateDead                        // 死亡
	MonsterStateRespawn                     // 重生中
)

// Monster 怪物
type Monster struct {
	// 基础信息
	ID     int32       `json:"id"`
	TypeID int32       `json:"type_id"`
	Name   string      `json:"name"`
	Type   MonsterType `json:"type"`
	Level  int32       `json:"level"`

	// 属性
	HP           int32 `json:"hp"`
	MaxHP        int32 `json:"max_hp"`
	MP           int32 `json:"mp"`
	MaxMP        int32 `json:"max_mp"`
	Attack       int32 `json:"attack"`
	Defense      int32 `json:"defense"`
	MagicAttack  int32 `json:"magic_attack"`
	MagicDefense int32 `json:"magic_defense"`
	Accuracy     int32 `json:"accuracy"`
	Agility      int32 `json:"agility"`

	// 位置信息
	MapID     string `json:"map_id"`
	X         int32  `json:"x"`
	Y         int32  `json:"y"`
	Direction int32  `json:"direction"`
	SpawnX    int32  `json:"spawn_x"` // 出生点X
	SpawnY    int32  `json:"spawn_y"` // 出生点Y

	// 状态
	State       MonsterState `json:"state"`
	Target      *Player      `json:"-"` // 当前目标
	LastAttack  time.Time    `json:"-"`
	RespawnTime time.Time    `json:"-"`

	// AI相关
	PatrolRange   int32     `json:"patrol_range"` // 巡逻范围
	AttackRange   int32     `json:"attack_range"` // 攻击范围
	ChaseRange    int32     `json:"chase_range"`  // 追击范围
	LastPatrol    time.Time `json:"-"`
	PatrolTargetX int32     `json:"-"`
	PatrolTargetY int32     `json:"-"`

	// 掉落
	ExpReward   int64 `json:"exp_reward"`
	DropTableID int32 `json:"drop_table_id"` // 掉落表ID

	// 运行时数据
	mutex      sync.RWMutex `json:"-"`
	logger     *zap.Logger  `json:"-"`
	gameWorld  *GameWorld   `json:"-"`
	dropSystem *DropSystem  `json:"-"`
	killer     *Player      `json:"-"` // 击杀者
}

// NewMonster 创建怪物
func NewMonster(typeID int32, name string, level int32, mapID string, x, y int32) *Monster {
	monster := &Monster{
		TypeID:      typeID,
		Name:        name,
		Type:        MonsterTypeNormal,
		Level:       level,
		MapID:       mapID,
		X:           x,
		Y:           y,
		SpawnX:      x,
		SpawnY:      y,
		Direction:   4,
		State:       MonsterStateIdle,
		PatrolRange: 5,
		AttackRange: 1,
		ChaseRange:  8,
		logger:      logger.GetLogger(),
	}

	// 根据等级初始化属性
	monster.initAttributes()

	// 根据等级设置掉落表
	monster.initDropTable()

	return monster
}

// initAttributes 初始化属性
func (m *Monster) initAttributes() {
	// 基础属性计算
	baseHP := 50 + m.Level*20
	baseAttack := 10 + m.Level*3
	baseDefense := 5 + m.Level*2

	// 根据怪物类型调整
	switch m.Type {
	case MonsterTypeElite:
		baseHP = int32(float32(baseHP) * 1.5)
		baseAttack = int32(float32(baseAttack) * 1.3)
		baseDefense = int32(float32(baseDefense) * 1.2)
	case MonsterTypeBoss:
		baseHP = int32(float32(baseHP) * 3.0)
		baseAttack = int32(float32(baseAttack) * 2.0)
		baseDefense = int32(float32(baseDefense) * 1.5)
	}

	m.MaxHP = baseHP
	m.HP = baseHP
	m.MaxMP = 20 + m.Level*5
	m.MP = m.MaxMP
	m.Attack = baseAttack
	m.Defense = baseDefense
	m.MagicAttack = baseAttack / 2
	m.MagicDefense = baseDefense
	m.Accuracy = 10 + m.Level
	m.Agility = 8 + m.Level/2

	// 经验奖励
	m.ExpReward = int64(m.Level * 10)
	if m.Type == MonsterTypeElite {
		m.ExpReward = int64(float64(m.ExpReward) * 1.5)
	} else if m.Type == MonsterTypeBoss {
		m.ExpReward = int64(float64(m.ExpReward) * 3.0)
	}
}

// initDropTable 初始化掉落表
func (m *Monster) initDropTable() {
	// 根据怪物等级和类型设置掉落表ID
	if m.Level <= 10 {
		m.DropTableID = 1 // 低级怪物掉落表
	} else if m.Level <= 25 {
		m.DropTableID = 2 // 中级怪物掉落表
	} else {
		m.DropTableID = 2 // 高级怪物也使用中级掉落表
	}

	// Boss怪物使用特殊掉落表
	if m.Type == MonsterTypeBoss {
		m.DropTableID = 3 // Boss掉落表
	}
}

// Update 更新怪物状态
func (m *Monster) Update(deltaTime time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	switch m.State {
	case MonsterStateIdle:
		m.updateIdle()
	case MonsterStatePatrol:
		m.updatePatrol()
	case MonsterStateCombat:
		m.updateCombat()
	case MonsterStateDead:
		m.updateDead()
	case MonsterStateRespawn:
		m.updateRespawn()
	}
}

// updateIdle 更新空闲状态
func (m *Monster) updateIdle() {
	// 寻找附近的玩家
	if target := m.findNearbyPlayer(); target != nil {
		m.Target = target
		m.State = MonsterStateCombat
		return
	}

	// 开始巡逻
	if time.Since(m.LastPatrol) > 5*time.Second {
		m.startPatrol()
	}
}

// updatePatrol 更新巡逻状态
func (m *Monster) updatePatrol() {
	// 寻找附近的玩家
	if target := m.findNearbyPlayer(); target != nil {
		m.Target = target
		m.State = MonsterStateCombat
		return
	}

	// 移动到巡逻目标点
	if m.X == m.PatrolTargetX && m.Y == m.PatrolTargetY {
		m.State = MonsterStateIdle
		m.LastPatrol = time.Now()
	} else {
		m.moveTowards(m.PatrolTargetX, m.PatrolTargetY)
	}
}

// updateCombat 更新战斗状态
func (m *Monster) updateCombat() {
	if m.Target == nil || !m.Target.IsAlive() {
		m.Target = nil
		m.State = MonsterStateIdle
		return
	}

	// 检查目标是否超出追击范围
	if m.getDistanceToTarget() > float64(m.ChaseRange) {
		m.Target = nil
		m.State = MonsterStateIdle
		return
	}

	// 攻击目标
	if m.getDistanceToTarget() <= float64(m.AttackRange) {
		if time.Since(m.LastAttack) > 2*time.Second {
			m.attackTarget()
			m.LastAttack = time.Now()
		}
	} else {
		// 追击目标
		m.moveTowards(m.Target.X, m.Target.Y)
	}
}

// updateDead 更新死亡状态
func (m *Monster) updateDead() {
	// 等待重生
	if time.Since(m.RespawnTime) > 30*time.Second {
		m.State = MonsterStateRespawn
	}
}

// updateRespawn 更新重生状态
func (m *Monster) updateRespawn() {
	// 重生
	m.HP = m.MaxHP
	m.MP = m.MaxMP
	m.X = m.SpawnX
	m.Y = m.SpawnY
	m.Target = nil
	m.State = MonsterStateIdle

	m.logger.Debug("Monster respawned",
		zap.Int32("monster_id", m.ID),
		zap.String("name", m.Name))
}

// findNearbyPlayer 寻找附近的玩家
func (m *Monster) findNearbyPlayer() *Player {
	if m.gameWorld == nil {
		return nil
	}

	// 获取地图上的玩家
	gameMap, exists := m.gameWorld.GetMap(m.MapID)
	if !exists {
		return nil
	}

	players := gameMap.GetPlayersInRange(m.X, m.Y, m.ChaseRange)
	if len(players) == 0 {
		return nil
	}

	// 返回最近的玩家
	var nearestPlayer *Player
	minDistance := float64(m.ChaseRange + 1)

	for _, player := range players {
		if !player.IsAlive() {
			continue
		}

		distance := m.getDistanceToPlayer(player)
		if distance < minDistance {
			minDistance = distance
			nearestPlayer = player
		}
	}

	return nearestPlayer
}

// startPatrol 开始巡逻
func (m *Monster) startPatrol() {
	// 随机选择巡逻目标点
	angle := rand.Float64() * 2 * math.Pi
	distance := rand.Float64() * float64(m.PatrolRange)

	m.PatrolTargetX = m.SpawnX + int32(math.Cos(angle)*distance)
	m.PatrolTargetY = m.SpawnY + int32(math.Sin(angle)*distance)

	m.State = MonsterStatePatrol
}

// moveTowards 向目标移动
func (m *Monster) moveTowards(targetX, targetY int32) {
	dx := targetX - m.X
	dy := targetY - m.Y

	if dx == 0 && dy == 0 {
		return
	}

	// 简单的移动逻辑
	if abs(dx) > abs(dy) {
		if dx > 0 {
			m.X++
			m.Direction = 6 // 右
		} else {
			m.X--
			m.Direction = 4 // 左
		}
	} else {
		if dy > 0 {
			m.Y++
			m.Direction = 2 // 下
		} else {
			m.Y--
			m.Direction = 0 // 上
		}
	}
}

// attackTarget 攻击目标
func (m *Monster) attackTarget() {
	if m.Target == nil {
		return
	}

	// 计算伤害
	damage := m.Attack + int32(rand.Intn(int(m.Attack/2)))
	damage -= m.Target.Defense / 2
	if damage < 1 {
		damage = 1
	}

	// 应用伤害
	isDead := m.Target.TakeDamage(damage)

	m.logger.Debug("Monster attacked player",
		zap.Int32("monster_id", m.ID),
		zap.Uint64("player_id", m.Target.ID),
		zap.Int32("damage", damage),
		zap.Bool("player_dead", isDead))

	if isDead {
		m.Target = nil
		m.State = MonsterStateIdle
	}
}

// TakeDamage 受到伤害
func (m *Monster) TakeDamage(damage int32, attacker *Player) bool {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.HP -= damage
	if m.HP <= 0 {
		m.HP = 0
		m.State = MonsterStateDead
		m.RespawnTime = time.Now()
		m.killer = attacker // 记录击杀者

		// 处理掉落
		m.processDrop()

		return true // 死亡
	}

	return false
}

// processDrop 处理掉落
func (m *Monster) processDrop() {
	if m.dropSystem == nil || m.killer == nil {
		return
	}

	// 处理掉落
	droppedItems := m.dropSystem.ProcessDrop(
		m.DropTableID,
		m.killer.Level,
		m.killer.Job,
		m.MapID,
		m.X,
		m.Y,
	)

	// 将掉落物品添加到地图
	if m.gameWorld != nil {
		gameMap, exists := m.gameWorld.GetMap(m.MapID)
		if exists && gameMap != nil {
			for _, item := range droppedItems {
				gameMap.AddDropItem(item)
			}
		}
	}

	// 给击杀者经验奖励
	if m.ExpReward > 0 {
		m.killer.AddExperience(m.ExpReward)
	}

	m.logger.Info("Monster killed",
		zap.Int32("monster_id", m.ID),
		zap.String("monster_name", m.Name),
		zap.Uint64("killer_id", m.killer.ID),
		zap.String("killer_name", m.killer.CharName),
		zap.Int64("exp_reward", m.ExpReward),
		zap.Int("dropped_items", len(droppedItems)))
}

// IsAlive 是否存活
func (m *Monster) IsAlive() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.State != MonsterStateDead && m.State != MonsterStateRespawn
}

// getDistanceToTarget 获取到目标的距离
func (m *Monster) getDistanceToTarget() float64 {
	if m.Target == nil {
		return 0
	}
	return m.getDistanceToPlayer(m.Target)
}

// getDistanceToPlayer 获取到玩家的距离
func (m *Monster) getDistanceToPlayer(player *Player) float64 {
	dx := float64(m.X - player.X)
	dy := float64(m.Y - player.Y)
	return math.Sqrt(dx*dx + dy*dy)
}

// abs 绝对值
func abs(x int32) int32 {
	if x < 0 {
		return -x
	}
	return x
}

// SetDropSystem 设置掉落系统
func (m *Monster) SetDropSystem(dropSystem *DropSystem) {
	m.dropSystem = dropSystem
}

// SetGameWorld 设置游戏世界
func (m *Monster) SetGameWorld(gameWorld *GameWorld) {
	m.gameWorld = gameWorld
}
