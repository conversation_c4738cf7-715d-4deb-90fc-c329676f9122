/* Autogenerated file, please don't edit */

#define ER_ERROR_FIRST 1000
#define ER_HASHCHK 1000
#define ER_NISAMCHK 1001
#define ER_NO 1002
#define ER_YES 1003
#define ER_CANT_CREATE_FILE 1004
#define ER_CANT_CREATE_TABLE 1005
#define ER_CANT_CREATE_DB 1006
#define ER_DB_CREATE_EXISTS 1007
#define ER_DB_DROP_EXISTS 1008
#define ER_DB_DROP_DELETE 1009
#define ER_DB_DROP_RMDIR 1010
#define ER_CANT_DELETE_FILE 1011
#define ER_CANT_FIND_SYSTEM_REC 1012
#define ER_CANT_GET_STAT 1013
#define ER_CANT_GET_WD 1014
#define ER_CANT_LOCK 1015
#define ER_CANT_OPEN_FILE 1016
#define ER_FILE_NOT_FOUND 1017
#define ER_CANT_READ_DIR 1018
#define ER_CANT_SET_WD 1019
#define ER_CHECKREAD 1020
#define ER_DISK_FULL 1021
#define ER_DUP_KEY 1022
#define ER_ERROR_ON_CLOSE 1023
#define ER_ERROR_ON_READ 1024
#define ER_ERROR_ON_RENAME 1025
#define ER_ERROR_ON_WRITE 1026
#define ER_FILE_USED 1027
#define ER_FILSORT_ABORT 1028
#define ER_FORM_NOT_FOUND 1029
#define ER_GET_ERRNO 1030
#define ER_ILLEGAL_HA 1031
#define ER_KEY_NOT_FOUND 1032
#define ER_NOT_FORM_FILE 1033
#define ER_NOT_KEYFILE 1034
#define ER_OLD_KEYFILE 1035
#define ER_OPEN_AS_READONLY 1036
#define ER_OUTOFMEMORY 1037
#define ER_OUT_OF_SORTMEMORY 1038
#define ER_UNEXPECTED_EOF 1039
#define ER_CON_COUNT_ERROR 1040
#define ER_OUT_OF_RESOURCES 1041
#define ER_BAD_HOST_ERROR 1042
#define ER_HANDSHAKE_ERROR 1043
#define ER_DBACCESS_DENIED_ERROR 1044
#define ER_ACCESS_DENIED_ERROR 1045
#define ER_NO_DB_ERROR 1046
#define ER_UNKNOWN_COM_ERROR 1047
#define ER_BAD_NULL_ERROR 1048
#define ER_BAD_DB_ERROR 1049
#define ER_TABLE_EXISTS_ERROR 1050
#define ER_BAD_TABLE_ERROR 1051
#define ER_NON_UNIQ_ERROR 1052
#define ER_SERVER_SHUTDOWN 1053
#define ER_BAD_FIELD_ERROR 1054
#define ER_WRONG_FIELD_WITH_GROUP 1055
#define ER_WRONG_GROUP_FIELD 1056
#define ER_WRONG_SUM_SELECT 1057
#define ER_WRONG_VALUE_COUNT 1058
#define ER_TOO_LONG_IDENT 1059
#define ER_DUP_FIELDNAME 1060
#define ER_DUP_KEYNAME 1061
#define ER_DUP_ENTRY 1062
#define ER_WRONG_FIELD_SPEC 1063
#define ER_PARSE_ERROR 1064
#define ER_EMPTY_QUERY 1065
#define ER_NONUNIQ_TABLE 1066
#define ER_INVALID_DEFAULT 1067
#define ER_MULTIPLE_PRI_KEY 1068
#define ER_TOO_MANY_KEYS 1069
#define ER_TOO_MANY_KEY_PARTS 1070
#define ER_TOO_LONG_KEY 1071
#define ER_KEY_COLUMN_DOES_NOT_EXITS 1072
#define ER_BLOB_USED_AS_KEY 1073
#define ER_TOO_BIG_FIELDLENGTH 1074
#define ER_WRONG_AUTO_KEY 1075
#define ER_READY 1076
#define ER_NORMAL_SHUTDOWN 1077
#define ER_GOT_SIGNAL 1078
#define ER_SHUTDOWN_COMPLETE 1079
#define ER_FORCING_CLOSE 1080
#define ER_IPSOCK_ERROR 1081
#define ER_NO_SUCH_INDEX 1082
#define ER_WRONG_FIELD_TERMINATORS 1083
#define ER_BLOBS_AND_NO_TERMINATED 1084
#define ER_TEXTFILE_NOT_READABLE 1085
#define ER_FILE_EXISTS_ERROR 1086
#define ER_LOAD_INFO 1087
#define ER_ALTER_INFO 1088
#define ER_WRONG_SUB_KEY 1089
#define ER_CANT_REMOVE_ALL_FIELDS 1090
#define ER_CANT_DROP_FIELD_OR_KEY 1091
#define ER_INSERT_INFO 1092
#define ER_UPDATE_TABLE_USED 1093
#define ER_NO_SUCH_THREAD 1094
#define ER_KILL_DENIED_ERROR 1095
#define ER_NO_TABLES_USED 1096
#define ER_TOO_BIG_SET 1097
#define ER_NO_UNIQUE_LOGFILE 1098
#define ER_TABLE_NOT_LOCKED_FOR_WRITE 1099
#define ER_TABLE_NOT_LOCKED 1100
#define ER_BLOB_CANT_HAVE_DEFAULT 1101
#define ER_WRONG_DB_NAME 1102
#define ER_WRONG_TABLE_NAME 1103
#define ER_TOO_BIG_SELECT 1104
#define ER_UNKNOWN_ERROR 1105
#define ER_UNKNOWN_PROCEDURE 1106
#define ER_WRONG_PARAMCOUNT_TO_PROCEDURE 1107
#define ER_WRONG_PARAMETERS_TO_PROCEDURE 1108
#define ER_UNKNOWN_TABLE 1109
#define ER_FIELD_SPECIFIED_TWICE 1110
#define ER_INVALID_GROUP_FUNC_USE 1111
#define ER_UNSUPPORTED_EXTENSION 1112
#define ER_TABLE_MUST_HAVE_COLUMNS 1113
#define ER_RECORD_FILE_FULL 1114
#define ER_UNKNOWN_CHARACTER_SET 1115
#define ER_TOO_MANY_TABLES 1116
#define ER_TOO_MANY_FIELDS 1117
#define ER_TOO_BIG_ROWSIZE 1118
#define ER_STACK_OVERRUN 1119
#define ER_WRONG_OUTER_JOIN 1120
#define ER_NULL_COLUMN_IN_INDEX 1121
#define ER_CANT_FIND_UDF 1122
#define ER_CANT_INITIALIZE_UDF 1123
#define ER_UDF_NO_PATHS 1124
#define ER_UDF_EXISTS 1125
#define ER_CANT_OPEN_LIBRARY 1126
#define ER_CANT_FIND_DL_ENTRY 1127
#define ER_FUNCTION_NOT_DEFINED 1128
#define ER_HOST_IS_BLOCKED 1129
#define ER_HOST_NOT_PRIVILEGED 1130
#define ER_PASSWORD_ANONYMOUS_USER 1131
#define ER_PASSWORD_NOT_ALLOWED 1132
#define ER_PASSWORD_NO_MATCH 1133
#define ER_UPDATE_INFO 1134
#define ER_CANT_CREATE_THREAD 1135
#define ER_WRONG_VALUE_COUNT_ON_ROW 1136
#define ER_CANT_REOPEN_TABLE 1137
#define ER_INVALID_USE_OF_NULL 1138
#define ER_REGEXP_ERROR 1139
#define ER_MIX_OF_GROUP_FUNC_AND_FIELDS 1140
#define ER_NONEXISTING_GRANT 1141
#define ER_TABLEACCESS_DENIED_ERROR 1142
#define ER_COLUMNACCESS_DENIED_ERROR 1143
#define ER_ILLEGAL_GRANT_FOR_TABLE 1144
#define ER_GRANT_WRONG_HOST_OR_USER 1145
#define ER_NO_SUCH_TABLE 1146
#define ER_NONEXISTING_TABLE_GRANT 1147
#define ER_NOT_ALLOWED_COMMAND 1148
#define ER_SYNTAX_ERROR 1149
#define ER_DELAYED_CANT_CHANGE_LOCK 1150
#define ER_TOO_MANY_DELAYED_THREADS 1151
#define ER_ABORTING_CONNECTION 1152
#define ER_NET_PACKET_TOO_LARGE 1153
#define ER_NET_READ_ERROR_FROM_PIPE 1154
#define ER_NET_FCNTL_ERROR 1155
#define ER_NET_PACKETS_OUT_OF_ORDER 1156
#define ER_NET_UNCOMPRESS_ERROR 1157
#define ER_NET_READ_ERROR 1158
#define ER_NET_READ_INTERRUPTED 1159
#define ER_NET_ERROR_ON_WRITE 1160
#define ER_NET_WRITE_INTERRUPTED 1161
#define ER_TOO_LONG_STRING 1162
#define ER_TABLE_CANT_HANDLE_BLOB 1163
#define ER_TABLE_CANT_HANDLE_AUTO_INCREMENT 1164
#define ER_DELAYED_INSERT_TABLE_LOCKED 1165
#define ER_WRONG_COLUMN_NAME 1166
#define ER_WRONG_KEY_COLUMN 1167
#define ER_WRONG_MRG_TABLE 1168
#define ER_DUP_UNIQUE 1169
#define ER_BLOB_KEY_WITHOUT_LENGTH 1170
#define ER_PRIMARY_CANT_HAVE_NULL 1171
#define ER_TOO_MANY_ROWS 1172
#define ER_REQUIRES_PRIMARY_KEY 1173
#define ER_NO_RAID_COMPILED 1174
#define ER_UPDATE_WITHOUT_KEY_IN_SAFE_MODE 1175
#define ER_KEY_DOES_NOT_EXITS 1176
#define ER_CHECK_NO_SUCH_TABLE 1177
#define ER_CHECK_NOT_IMPLEMENTED 1178
#define ER_CANT_DO_THIS_DURING_AN_TRANSACTION 1179
#define ER_ERROR_DURING_COMMIT 1180
#define ER_ERROR_DURING_ROLLBACK 1181
#define ER_ERROR_DURING_FLUSH_LOGS 1182
#define ER_ERROR_DURING_CHECKPOINT 1183
#define ER_NEW_ABORTING_CONNECTION 1184
#define ER_DUMP_NOT_IMPLEMENTED 1185
#define ER_FLUSH_MASTER_BINLOG_CLOSED 1186
#define ER_INDEX_REBUILD 1187
#define ER_MASTER 1188
#define ER_MASTER_NET_READ 1189
#define ER_MASTER_NET_WRITE 1190
#define ER_FT_MATCHING_KEY_NOT_FOUND 1191
#define ER_LOCK_OR_ACTIVE_TRANSACTION 1192
#define ER_UNKNOWN_SYSTEM_VARIABLE 1193
#define ER_CRASHED_ON_USAGE 1194
#define ER_CRASHED_ON_REPAIR 1195
#define ER_WARNING_NOT_COMPLETE_ROLLBACK 1196
#define ER_TRANS_CACHE_FULL 1197
#define ER_SLAVE_MUST_STOP 1198
#define ER_SLAVE_NOT_RUNNING 1199
#define ER_BAD_SLAVE 1200
#define ER_MASTER_INFO 1201
#define ER_SLAVE_THREAD 1202
#define ER_TOO_MANY_USER_CONNECTIONS 1203
#define ER_SET_CONSTANTS_ONLY 1204
#define ER_LOCK_WAIT_TIMEOUT 1205
#define ER_LOCK_TABLE_FULL 1206
#define ER_READ_ONLY_TRANSACTION 1207
#define ER_DROP_DB_WITH_READ_LOCK 1208
#define ER_CREATE_DB_WITH_READ_LOCK 1209
#define ER_WRONG_ARGUMENTS 1210
#define ER_NO_PERMISSION_TO_CREATE_USER 1211
#define ER_UNION_TABLES_IN_DIFFERENT_DIR 1212
#define ER_LOCK_DEADLOCK 1213
#define ER_TABLE_CANT_HANDLE_FT 1214
#define ER_CANNOT_ADD_FOREIGN 1215
#define ER_NO_REFERENCED_ROW 1216
#define ER_ROW_IS_REFERENCED 1217
#define ER_CONNECT_TO_MASTER 1218
#define ER_QUERY_ON_MASTER 1219
#define ER_ERROR_WHEN_EXECUTING_COMMAND 1220
#define ER_WRONG_USAGE 1221
#define ER_WRONG_NUMBER_OF_COLUMNS_IN_SELECT 1222
#define ER_CANT_UPDATE_WITH_READLOCK 1223
#define ER_MIXING_NOT_ALLOWED 1224
#define ER_DUP_ARGUMENT 1225
#define ER_USER_LIMIT_REACHED 1226
#define ER_SPECIFIC_ACCESS_DENIED_ERROR 1227
#define ER_LOCAL_VARIABLE 1228
#define ER_GLOBAL_VARIABLE 1229
#define ER_NO_DEFAULT 1230
#define ER_WRONG_VALUE_FOR_VAR 1231
#define ER_WRONG_TYPE_FOR_VAR 1232
#define ER_VAR_CANT_BE_READ 1233
#define ER_CANT_USE_OPTION_HERE 1234
#define ER_NOT_SUPPORTED_YET 1235
#define ER_MASTER_FATAL_ERROR_READING_BINLOG 1236
#define ER_SLAVE_IGNORED_TABLE 1237
#define ER_INCORRECT_GLOBAL_LOCAL_VAR 1238
#define ER_WRONG_FK_DEF 1239
#define ER_KEY_REF_DO_NOT_MATCH_TABLE_REF 1240
#define ER_OPERAND_COLUMNS 1241
#define ER_SUBQUERY_NO_1_ROW 1242
#define ER_UNKNOWN_STMT_HANDLER 1243
#define ER_CORRUPT_HELP_DB 1244
#define ER_CYCLIC_REFERENCE 1245
#define ER_AUTO_CONVERT 1246
#define ER_ILLEGAL_REFERENCE 1247
#define ER_DERIVED_MUST_HAVE_ALIAS 1248
#define ER_SELECT_REDUCED 1249
#define ER_TABLENAME_NOT_ALLOWED_HERE 1250
#define ER_NOT_SUPPORTED_AUTH_MODE 1251
#define ER_SPATIAL_CANT_HAVE_NULL 1252
#define ER_COLLATION_CHARSET_MISMATCH 1253
#define ER_SLAVE_WAS_RUNNING 1254
#define ER_SLAVE_WAS_NOT_RUNNING 1255
#define ER_TOO_BIG_FOR_UNCOMPRESS 1256
#define ER_ZLIB_Z_MEM_ERROR 1257
#define ER_ZLIB_Z_BUF_ERROR 1258
#define ER_ZLIB_Z_DATA_ERROR 1259
#define ER_CUT_VALUE_GROUP_CONCAT 1260
#define ER_WARN_TOO_FEW_RECORDS 1261
#define ER_WARN_TOO_MANY_RECORDS 1262
#define ER_WARN_NULL_TO_NOTNULL 1263
#define ER_WARN_DATA_OUT_OF_RANGE 1264
#define WARN_DATA_TRUNCATED 1265
#define ER_WARN_USING_OTHER_HANDLER 1266
#define ER_CANT_AGGREGATE_2COLLATIONS 1267
#define ER_DROP_USER 1268
#define ER_REVOKE_GRANTS 1269
#define ER_CANT_AGGREGATE_3COLLATIONS 1270
#define ER_CANT_AGGREGATE_NCOLLATIONS 1271
#define ER_VARIABLE_IS_NOT_STRUCT 1272
#define ER_UNKNOWN_COLLATION 1273
#define ER_SLAVE_IGNORED_SSL_PARAMS 1274
#define ER_SERVER_IS_IN_SECURE_AUTH_MODE 1275
#define ER_WARN_FIELD_RESOLVED 1276
#define ER_BAD_SLAVE_UNTIL_COND 1277
#define ER_MISSING_SKIP_SLAVE 1278
#define ER_UNTIL_COND_IGNORED 1279
#define ER_WRONG_NAME_FOR_INDEX 1280
#define ER_WRONG_NAME_FOR_CATALOG 1281
#define ER_WARN_QC_RESIZE 1282
#define ER_BAD_FT_COLUMN 1283
#define ER_UNKNOWN_KEY_CACHE 1284
#define ER_WARN_HOSTNAME_WONT_WORK 1285
#define ER_UNKNOWN_STORAGE_ENGINE 1286
#define ER_WARN_DEPRECATED_SYNTAX 1287
#define ER_NON_UPDATABLE_TABLE 1288
#define ER_FEATURE_DISABLED 1289
#define ER_OPTION_PREVENTS_STATEMENT 1290
#define ER_DUPLICATED_VALUE_IN_TYPE 1291
#define ER_TRUNCATED_WRONG_VALUE 1292
#define ER_TOO_MUCH_AUTO_TIMESTAMP_COLS 1293
#define ER_INVALID_ON_UPDATE 1294
#define ER_UNSUPPORTED_PS 1295
#define ER_GET_ERRMSG 1296
#define ER_GET_TEMPORARY_ERRMSG 1297
#define ER_UNKNOWN_TIME_ZONE 1298
#define ER_WARN_INVALID_TIMESTAMP 1299
#define ER_INVALID_CHARACTER_STRING 1300
#define ER_WARN_ALLOWED_PACKET_OVERFLOWED 1301
#define ER_CONFLICTING_DECLARATIONS 1302
#define ER_SP_NO_RECURSIVE_CREATE 1303
#define ER_SP_ALREADY_EXISTS 1304
#define ER_SP_DOES_NOT_EXIST 1305
#define ER_SP_DROP_FAILED 1306
#define ER_SP_STORE_FAILED 1307
#define ER_SP_LILABEL_MISMATCH 1308
#define ER_SP_LABEL_REDEFINE 1309
#define ER_SP_LABEL_MISMATCH 1310
#define ER_SP_UNINIT_VAR 1311
#define ER_SP_BADSELECT 1312
#define ER_SP_BADRETURN 1313
#define ER_SP_BADSTATEMENT 1314
#define ER_UPDATE_LOG_DEPRECATED_IGNORED 1315
#define ER_UPDATE_LOG_DEPRECATED_TRANSLATED 1316
#define ER_QUERY_INTERRUPTED 1317
#define ER_SP_WRONG_NO_OF_ARGS 1318
#define ER_SP_COND_MISMATCH 1319
#define ER_SP_NORETURN 1320
#define ER_SP_NORETURNEND 1321
#define ER_SP_BAD_CURSOR_QUERY 1322
#define ER_SP_BAD_CURSOR_SELECT 1323
#define ER_SP_CURSOR_MISMATCH 1324
#define ER_SP_CURSOR_ALREADY_OPEN 1325
#define ER_SP_CURSOR_NOT_OPEN 1326
#define ER_SP_UNDECLARED_VAR 1327
#define ER_SP_WRONG_NO_OF_FETCH_ARGS 1328
#define ER_SP_FETCH_NO_DATA 1329
#define ER_SP_DUP_PARAM 1330
#define ER_SP_DUP_VAR 1331
#define ER_SP_DUP_COND 1332
#define ER_SP_DUP_CURS 1333
#define ER_SP_CANT_ALTER 1334
#define ER_SP_SUBSELECT_NYI 1335
#define ER_STMT_NOT_ALLOWED_IN_SF_OR_TRG 1336
#define ER_SP_VARCOND_AFTER_CURSHNDLR 1337
#define ER_SP_CURSOR_AFTER_HANDLER 1338
#define ER_SP_CASE_NOT_FOUND 1339
#define ER_FPARSER_TOO_BIG_FILE 1340
#define ER_FPARSER_BAD_HEADER 1341
#define ER_FPARSER_EOF_IN_COMMENT 1342
#define ER_FPARSER_ERROR_IN_PARAMETER 1343
#define ER_FPARSER_EOF_IN_UNKNOWN_PARAMETER 1344
#define ER_VIEW_NO_EXPLAIN 1345
#define ER_FRM_UNKNOWN_TYPE 1346
#define ER_WRONG_OBJECT 1347
#define ER_NONUPDATEABLE_COLUMN 1348
#define ER_VIEW_SELECT_DERIVED 1349
#define ER_VIEW_SELECT_CLAUSE 1350
#define ER_VIEW_SELECT_VARIABLE 1351
#define ER_VIEW_SELECT_TMPTABLE 1352
#define ER_VIEW_WRONG_LIST 1353
#define ER_WARN_VIEW_MERGE 1354
#define ER_WARN_VIEW_WITHOUT_KEY 1355
#define ER_VIEW_INVALID 1356
#define ER_SP_NO_DROP_SP 1357
#define ER_SP_GOTO_IN_HNDLR 1358
#define ER_TRG_ALREADY_EXISTS 1359
#define ER_TRG_DOES_NOT_EXIST 1360
#define ER_TRG_ON_VIEW_OR_TEMP_TABLE 1361
#define ER_TRG_CANT_CHANGE_ROW 1362
#define ER_TRG_NO_SUCH_ROW_IN_TRG 1363
#define ER_NO_DEFAULT_FOR_FIELD 1364
#define ER_DIVISION_BY_ZERO 1365
#define ER_TRUNCATED_WRONG_VALUE_FOR_FIELD 1366
#define ER_ILLEGAL_VALUE_FOR_TYPE 1367
#define ER_VIEW_NONUPD_CHECK 1368
#define ER_VIEW_CHECK_FAILED 1369
#define ER_PROCACCESS_DENIED_ERROR 1370
#define ER_RELAY_LOG_FAIL 1371
#define ER_PASSWD_LENGTH 1372
#define ER_UNKNOWN_TARGET_BINLOG 1373
#define ER_IO_ERR_LOG_INDEX_READ 1374
#define ER_BINLOG_PURGE_PROHIBITED 1375
#define ER_FSEEK_FAIL 1376
#define ER_BINLOG_PURGE_FATAL_ERR 1377
#define ER_LOG_IN_USE 1378
#define ER_LOG_PURGE_UNKNOWN_ERR 1379
#define ER_RELAY_LOG_INIT 1380
#define ER_NO_BINARY_LOGGING 1381
#define ER_RESERVED_SYNTAX 1382
#define ER_WSAS_FAILED 1383
#define ER_DIFF_GROUPS_PROC 1384
#define ER_NO_GROUP_FOR_PROC 1385
#define ER_ORDER_WITH_PROC 1386
#define ER_LOGGING_PROHIBIT_CHANGING_OF 1387
#define ER_NO_FILE_MAPPING 1388
#define ER_WRONG_MAGIC 1389
#define ER_PS_MANY_PARAM 1390
#define ER_KEY_PART_0 1391
#define ER_VIEW_CHECKSUM 1392
#define ER_VIEW_MULTIUPDATE 1393
#define ER_VIEW_NO_INSERT_FIELD_LIST 1394
#define ER_VIEW_DELETE_MERGE_VIEW 1395
#define ER_CANNOT_USER 1396
#define ER_XAER_NOTA 1397
#define ER_XAER_INVAL 1398
#define ER_XAER_RMFAIL 1399
#define ER_XAER_OUTSIDE 1400
#define ER_XAER_RMERR 1401
#define ER_XA_RBROLLBACK 1402
#define ER_NONEXISTING_PROC_GRANT 1403
#define ER_PROC_AUTO_GRANT_FAIL 1404
#define ER_PROC_AUTO_REVOKE_FAIL 1405
#define ER_DATA_TOO_LONG 1406
#define ER_SP_BAD_SQLSTATE 1407
#define ER_STARTUP 1408
#define ER_LOAD_FROM_FIXED_SIZE_ROWS_TO_VAR 1409
#define ER_CANT_CREATE_USER_WITH_GRANT 1410
#define ER_WRONG_VALUE_FOR_TYPE 1411
#define ER_TABLE_DEF_CHANGED 1412
#define ER_SP_DUP_HANDLER 1413
#define ER_SP_NOT_VAR_ARG 1414
#define ER_SP_NO_RETSET 1415
#define ER_CANT_CREATE_GEOMETRY_OBJECT 1416
#define ER_FAILED_ROUTINE_BREAK_BINLOG 1417
#define ER_BINLOG_UNSAFE_ROUTINE 1418
#define ER_BINLOG_CREATE_ROUTINE_NEED_SUPER 1419
#define ER_EXEC_STMT_WITH_OPEN_CURSOR 1420
#define ER_STMT_HAS_NO_OPEN_CURSOR 1421
#define ER_COMMIT_NOT_ALLOWED_IN_SF_OR_TRG 1422
#define ER_NO_DEFAULT_FOR_VIEW_FIELD 1423
#define ER_SP_NO_RECURSION 1424
#define ER_TOO_BIG_SCALE 1425
#define ER_TOO_BIG_PRECISION 1426
#define ER_M_BIGGER_THAN_D 1427
#define ER_WRONG_LOCK_OF_SYSTEM_TABLE 1428
#define ER_CONNECT_TO_FOREIGN_DATA_SOURCE 1429
#define ER_QUERY_ON_FOREIGN_DATA_SOURCE 1430
#define ER_FOREIGN_DATA_SOURCE_DOESNT_EXIST 1431
#define ER_FOREIGN_DATA_STRING_INVALID_CANT_CREATE 1432
#define ER_FOREIGN_DATA_STRING_INVALID 1433
#define ER_CANT_CREATE_FEDERATED_TABLE 1434
#define ER_TRG_IN_WRONG_SCHEMA 1435
#define ER_STACK_OVERRUN_NEED_MORE 1436
#define ER_TOO_LONG_BODY 1437
#define ER_WARN_CANT_DROP_DEFAULT_KEYCACHE 1438
#define ER_TOO_BIG_DISPLAYWIDTH 1439
#define ER_XAER_DUPID 1440
#define ER_DATETIME_FUNCTION_OVERFLOW 1441
#define ER_CANT_UPDATE_USED_TABLE_IN_SF_OR_TRG 1442
#define ER_VIEW_PREVENT_UPDATE 1443
#define ER_PS_NO_RECURSION 1444
#define ER_SP_CANT_SET_AUTOCOMMIT 1445
#define ER_MALFORMED_DEFINER 1446
#define ER_VIEW_FRM_NO_USER 1447
#define ER_VIEW_OTHER_USER 1448
#define ER_NO_SUCH_USER 1449
#define ER_FORBID_SCHEMA_CHANGE 1450
#define ER_ROW_IS_REFERENCED_2 1451
#define ER_NO_REFERENCED_ROW_2 1452
#define ER_SP_BAD_VAR_SHADOW 1453
#define ER_TRG_NO_DEFINER 1454
#define ER_OLD_FILE_FORMAT 1455
#define ER_SP_RECURSION_LIMIT 1456
#define ER_SP_PROC_TABLE_CORRUPT 1457
#define ER_SP_WRONG_NAME 1458
#define ER_TABLE_NEEDS_UPGRADE 1459
#define ER_SP_NO_AGGREGATE 1460
#define ER_MAX_PREPARED_STMT_COUNT_REACHED 1461
#define ER_VIEW_RECURSIVE 1462
#define ER_NON_GROUPING_FIELD_USED 1463
#define ER_TABLE_CANT_HANDLE_SPKEYS 1464
#define ER_NO_TRIGGERS_ON_SYSTEM_SCHEMA 1465
#define ER_REMOVED_SPACES 1466
#define ER_AUTOINC_READ_FAILED 1467
#define ER_USERNAME 1468
#define ER_HOSTNAME 1469
#define ER_WRONG_STRING_LENGTH 1470
#define ER_NON_INSERTABLE_TABLE 1471
#define ER_ADMIN_WRONG_MRG_TABLE 1472
#define ER_TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT 1473
#define ER_NAME_BECOMES_EMPTY 1474
#define ER_AMBIGUOUS_FIELD_TERM 1475
#define ER_FOREIGN_SERVER_EXISTS 1476
#define ER_FOREIGN_SERVER_DOESNT_EXIST 1477
#define ER_ILLEGAL_HA_CREATE_OPTION 1478
#define ER_PARTITION_REQUIRES_VALUES_ERROR 1479
#define ER_PARTITION_WRONG_VALUES_ERROR 1480
#define ER_PARTITION_MAXVALUE_ERROR 1481
#define ER_PARTITION_SUBPARTITION_ERROR 1482
#define ER_PARTITION_SUBPART_MIX_ERROR 1483
#define ER_PARTITION_WRONG_NO_PART_ERROR 1484
#define ER_PARTITION_WRONG_NO_SUBPART_ERROR 1485
#define ER_WRONG_EXPR_IN_PARTITION_FUNC_ERROR 1486
#define ER_NO_CONST_EXPR_IN_RANGE_OR_LIST_ERROR 1487
#define ER_FIELD_NOT_FOUND_PART_ERROR 1488
#define ER_LIST_OF_FIELDS_ONLY_IN_HASH_ERROR 1489
#define ER_INCONSISTENT_PARTITION_INFO_ERROR 1490
#define ER_PARTITION_FUNC_NOT_ALLOWED_ERROR 1491
#define ER_PARTITIONS_MUST_BE_DEFINED_ERROR 1492
#define ER_RANGE_NOT_INCREASING_ERROR 1493
#define ER_INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR 1494
#define ER_MULTIPLE_DEF_CONST_IN_LIST_PART_ERROR 1495
#define ER_PARTITION_ENTRY_ERROR 1496
#define ER_MIX_HANDLER_ERROR 1497
#define ER_PARTITION_NOT_DEFINED_ERROR 1498
#define ER_TOO_MANY_PARTITIONS_ERROR 1499
#define ER_SUBPARTITION_ERROR 1500
#define ER_CANT_CREATE_HANDLER_FILE 1501
#define ER_BLOB_FIELD_IN_PART_FUNC_ERROR 1502
#define ER_UNIQUE_KEY_NEED_ALL_FIELDS_IN_PF 1503
#define ER_NO_PARTS_ERROR 1504
#define ER_PARTITION_MGMT_ON_NONPARTITIONED 1505
#define ER_FOREIGN_KEY_ON_PARTITIONED 1506
#define ER_DROP_PARTITION_NON_EXISTENT 1507
#define ER_DROP_LAST_PARTITION 1508
#define ER_COALESCE_ONLY_ON_HASH_PARTITION 1509
#define ER_REORG_HASH_ONLY_ON_SAME_NO 1510
#define ER_REORG_NO_PARAM_ERROR 1511
#define ER_ONLY_ON_RANGE_LIST_PARTITION 1512
#define ER_ADD_PARTITION_SUBPART_ERROR 1513
#define ER_ADD_PARTITION_NO_NEW_PARTITION 1514
#define ER_COALESCE_PARTITION_NO_PARTITION 1515
#define ER_REORG_PARTITION_NOT_EXIST 1516
#define ER_SAME_NAME_PARTITION 1517
#define ER_NO_BINLOG_ERROR 1518
#define ER_CONSECUTIVE_REORG_PARTITIONS 1519
#define ER_REORG_OUTSIDE_RANGE 1520
#define ER_PARTITION_FUNCTION_FAILURE 1521
#define ER_PART_STATE_ERROR 1522
#define ER_LIMITED_PART_RANGE 1523
#define ER_PLUGIN_IS_NOT_LOADED 1524
#define ER_WRONG_VALUE 1525
#define ER_NO_PARTITION_FOR_GIVEN_VALUE 1526
#define ER_FILEGROUP_OPTION_ONLY_ONCE 1527
#define ER_CREATE_FILEGROUP_FAILED 1528
#define ER_DROP_FILEGROUP_FAILED 1529
#define ER_TABLESPACE_AUTO_EXTEND_ERROR 1530
#define ER_WRONG_SIZE_NUMBER 1531
#define ER_SIZE_OVERFLOW_ERROR 1532
#define ER_ALTER_FILEGROUP_FAILED 1533
#define ER_BINLOG_ROW_LOGGING_FAILED 1534
#define ER_BINLOG_ROW_WRONG_TABLE_DEF 1535
#define ER_BINLOG_ROW_RBR_TO_SBR 1536
#define ER_EVENT_ALREADY_EXISTS 1537
#define ER_EVENT_STORE_FAILED 1538
#define ER_EVENT_DOES_NOT_EXIST 1539
#define ER_EVENT_CANT_ALTER 1540
#define ER_EVENT_DROP_FAILED 1541
#define ER_EVENT_INTERVAL_NOT_POSITIVE_OR_TOO_BIG 1542
#define ER_EVENT_ENDS_BEFORE_STARTS 1543
#define ER_EVENT_EXEC_TIME_IN_THE_PAST 1544
#define ER_EVENT_OPEN_TABLE_FAILED 1545
#define ER_EVENT_NEITHER_M_EXPR_NOR_M_AT 1546
#define ER_COL_COUNT_DOESNT_MATCH_CORRUPTED 1547
#define ER_CANNOT_LOAD_FROM_TABLE 1548
#define ER_EVENT_CANNOT_DELETE 1549
#define ER_EVENT_COMPILE_ERROR 1550
#define ER_EVENT_SAME_NAME 1551
#define ER_EVENT_DATA_TOO_LONG 1552
#define ER_DROP_INDEX_FK 1553
#define ER_WARN_DEPRECATED_SYNTAX_WITH_VER 1554
#define ER_CANT_WRITE_LOCK_LOG_TABLE 1555
#define ER_CANT_LOCK_LOG_TABLE 1556
#define ER_FOREIGN_DUPLICATE_KEY 1557
#define ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE 1558
#define ER_TEMP_TABLE_PREVENTS_SWITCH_OUT_OF_RBR 1559
#define ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_FORMAT 1560
#define ER_NDB_CANT_SWITCH_BINLOG_FORMAT 1561
#define ER_PARTITION_NO_TEMPORARY 1562
#define ER_PARTITION_CONST_DOMAIN_ERROR 1563
#define ER_PARTITION_FUNCTION_IS_NOT_ALLOWED 1564
#define ER_DDL_LOG_ERROR 1565
#define ER_NULL_IN_VALUES_LESS_THAN 1566
#define ER_WRONG_PARTITION_NAME 1567
#define ER_CANT_CHANGE_TX_ISOLATION 1568
#define ER_DUP_ENTRY_AUTOINCREMENT_CASE 1569
#define ER_EVENT_MODIFY_QUEUE_ERROR 1570
#define ER_EVENT_SET_VAR_ERROR 1571
#define ER_PARTITION_MERGE_ERROR 1572
#define ER_CANT_ACTIVATE_LOG 1573
#define ER_RBR_NOT_AVAILABLE 1574
#define ER_BASE64_DECODE_ERROR 1575
#define ER_EVENT_RECURSION_FORBIDDEN 1576
#define ER_EVENTS_DB_ERROR 1577
#define ER_ONLY_INTEGERS_ALLOWED 1578
#define ER_UNSUPORTED_LOG_ENGINE 1579
#define ER_BAD_LOG_STATEMENT 1580
#define ER_CANT_RENAME_LOG_TABLE 1581
#define ER_WRONG_PARAMCOUNT_TO_NATIVE_FCT 1582
#define ER_WRONG_PARAMETERS_TO_NATIVE_FCT 1583
#define ER_WRONG_PARAMETERS_TO_STORED_FCT 1584
#define ER_NATIVE_FCT_NAME_COLLISION 1585
#define ER_DUP_ENTRY_WITH_KEY_NAME 1586
#define ER_BINLOG_PURGE_EMFILE 1587
#define ER_EVENT_CANNOT_CREATE_IN_THE_PAST 1588
#define ER_EVENT_CANNOT_ALTER_IN_THE_PAST 1589
#define ER_SLAVE_INCIDENT 1590
#define ER_NO_PARTITION_FOR_GIVEN_VALUE_SILENT 1591
#define ER_BINLOG_UNSAFE_STATEMENT 1592
#define ER_SLAVE_FATAL_ERROR 1593
#define ER_SLAVE_RELAY_LOG_READ_FAILURE 1594
#define ER_SLAVE_RELAY_LOG_WRITE_FAILURE 1595
#define ER_SLAVE_CREATE_EVENT_FAILURE 1596
#define ER_SLAVE_MASTER_COM_FAILURE 1597
#define ER_BINLOG_LOGGING_IMPOSSIBLE 1598
#define ER_VIEW_NO_CREATION_CTX 1599
#define ER_VIEW_INVALID_CREATION_CTX 1600
#define ER_SR_INVALID_CREATION_CTX 1601
#define ER_TRG_CORRUPTED_FILE 1602
#define ER_TRG_NO_CREATION_CTX 1603
#define ER_TRG_INVALID_CREATION_CTX 1604
#define ER_EVENT_INVALID_CREATION_CTX 1605
#define ER_TRG_CANT_OPEN_TABLE 1606
#define ER_CANT_CREATE_SROUTINE 1607
#define ER_SLAVE_AMBIGOUS_EXEC_MODE 1608
#define ER_NO_FORMAT_DESCRIPTION_EVENT_BEFORE_BINLOG_STATEMENT 1609
#define ER_SLAVE_CORRUPT_EVENT 1610
#define ER_LOAD_DATA_INVALID_COLUMN 1611
#define ER_LOG_PURGE_NO_FILE 1612
#define ER_XA_RBTIMEOUT 1613
#define ER_XA_RBDEADLOCK 1614
#define ER_NEED_REPREPARE 1615
#define ER_DELAYED_NOT_SUPPORTED 1616
#define WARN_NO_MASTER_INFO 1617
#define WARN_OPTION_IGNORED 1618
#define WARN_PLUGIN_DELETE_BUILTIN 1619
#define WARN_PLUGIN_BUSY 1620
#define ER_VARIABLE_IS_READONLY 1621
#define ER_WARN_ENGINE_TRANSACTION_ROLLBACK 1622
#define ER_SLAVE_HEARTBEAT_FAILURE 1623
#define ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE 1624
#define ER_NDB_REPLICATION_SCHEMA_ERROR 1625
#define ER_CONFLICT_FN_PARSE_ERROR 1626
#define ER_EXCEPTIONS_WRITE_ERROR 1627
#define ER_TOO_LONG_TABLE_COMMENT 1628
#define ER_TOO_LONG_FIELD_COMMENT 1629
#define ER_FUNC_INEXISTENT_NAME_COLLISION 1630
#define ER_DATABASE_NAME 1631
#define ER_TABLE_NAME 1632
#define ER_PARTITION_NAME 1633
#define ER_SUBPARTITION_NAME 1634
#define ER_TEMPORARY_NAME 1635
#define ER_RENAMED_NAME 1636
#define ER_TOO_MANY_CONCURRENT_TRXS 1637
#define WARN_NON_ASCII_SEPARATOR_NOT_IMPLEMENTED 1638
#define ER_DEBUG_SYNC_TIMEOUT 1639
#define ER_DEBUG_SYNC_HIT_LIMIT 1640
#define ER_ERROR_LAST 1640
