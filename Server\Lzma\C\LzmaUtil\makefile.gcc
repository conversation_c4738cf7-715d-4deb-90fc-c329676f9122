PROG = lzma
CXX = g++
LIB =
RM = rm -f
CFLAGS = -c -O2 -Wall

OBJS = \
  LzmaUtil.o \
  Alloc.o \
  LzFind.o \
  LzmaDec.o \
  LzmaEnc.o \
  7zFile.o \
  7zStream.o \


all: $(PROG)

$(PROG): $(OBJS)
	$(CXX) -o $(PROG) $(LDFLAGS) $(OBJS) $(LIB) $(LIB2)

LzmaUtil.o: LzmaUtil.c
	$(CXX) $(CFLAGS) LzmaUtil.c

Alloc.o: ../Alloc.c
	$(CXX) $(CFLAGS) ../Alloc.c

LzFind.o: ../LzFind.c
	$(CXX) $(CFLAGS) ../LzFind.c

LzmaDec.o: ../LzmaDec.c
	$(CXX) $(CFLAGS) ../LzmaDec.c

LzmaEnc.o: ../LzmaEnc.c
	$(CXX) $(CFLAGS) ../LzmaEnc.c

7zFile.o: ../7zFile.c
	$(CXX) $(CFLAGS) ../7zFile.c

7zStream.o: ../7zStream.c
	$(CXX) $(CFLAGS) ../7zStream.c

clean:
	-$(RM) $(PROG) $(OBJS)
