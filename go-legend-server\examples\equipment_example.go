package main

import (
	"fmt"

	"github.com/legend-server/go-legend-server/internal/game"
)

func main() {
	fmt.Println("=== 传奇游戏装备系统演示 ===")

	// 创建测试玩家
	warrior := createWarriorWithEquipment()
	wizard := createWizardWithEquipment()

	// 显示初始状态
	fmt.Printf("\n=== 初始状态 ===\n")
	displayPlayerStatus(warrior)
	displayPlayerStatus(wizard)

	// 演示装备系统
	fmt.Printf("\n=== 装备系统演示 ===\n")
	demonstrateEquipmentSystem(warrior)

	// 演示装备耐久度
	fmt.Printf("\n=== 装备耐久度系统 ===\n")
	demonstrateDurabilitySystem(warrior)

	// 演示装备强化
	fmt.Printf("\n=== 装备强化系统 ===\n")
	demonstrateEnhancementSystem(warrior)

	// 演示战斗系统
	fmt.Printf("\n=== 装备对战斗的影响 ===\n")
	demonstrateCombatWithEquipment(warrior, wizard)

	fmt.Println("\n装备系统演示完成！")
}

// createWarriorWithEquipment 创建带装备的战士
func createWarriorWithEquipment() *game.Player {
	warrior := game.NewPlayer("account1", "装备战士", game.JobWarrior, game.GenderMale)
	warrior.ID = 1
	warrior.Level = 15
	warrior.HP = 200
	warrior.MaxHP = 200
	warrior.MP = 80
	warrior.MaxMP = 80
	warrior.Attack = 50
	warrior.Defense = 30
	warrior.MagicAttack = 20
	warrior.MagicDefense = 25
	warrior.Accuracy = 30
	warrior.Agility = 20

	// 创建并装备武器
	weapon := game.NewEquipment(1001, "精钢剑", game.EquipTypeWeapon, game.QualityUncommon)
	weapon.Attack = 35
	weapon.Accuracy = 10
	weapon.RequiredLevel = 10
	weapon.RequiredJob = game.JobWarrior
	warrior.EquipItem(weapon)

	// 创建并装备头盔
	helmet := game.NewEquipment(1002, "战士头盔", game.EquipTypeHelmet, game.QualityCommon)
	helmet.Defense = 20
	helmet.HPBonus = 30
	helmet.RequiredLevel = 10
	helmet.RequiredJob = game.JobWarrior
	warrior.EquipItem(helmet)

	// 创建并装备盔甲
	armor := game.NewEquipment(1003, "战士盔甲", game.EquipTypeArmor, game.QualityCommon)
	armor.Defense = 40
	armor.HPBonus = 50
	armor.RequiredLevel = 10
	armor.RequiredJob = game.JobWarrior
	warrior.EquipItem(armor)

	return warrior
}

// createWizardWithEquipment 创建带装备的法师
func createWizardWithEquipment() *game.Player {
	wizard := game.NewPlayer("account2", "装备法师", game.JobWizard, game.GenderFemale)
	wizard.ID = 2
	wizard.Level = 15
	wizard.HP = 120
	wizard.MaxHP = 120
	wizard.MP = 180
	wizard.MaxMP = 180
	wizard.Attack = 25
	wizard.Defense = 20
	wizard.MagicAttack = 60
	wizard.MagicDefense = 40
	wizard.Accuracy = 25
	wizard.Agility = 25

	// 创建并装备法杖
	staff := game.NewEquipment(2001, "魔法法杖", game.EquipTypeWeapon, game.QualityRare)
	staff.MagicAttack = 45
	staff.MPBonus = 40
	staff.RequiredLevel = 10
	staff.RequiredJob = game.JobWizard
	wizard.EquipItem(staff)

	// 创建并装备法师帽
	hat := game.NewEquipment(2002, "智慧法帽", game.EquipTypeHelmet, game.QualityUncommon)
	hat.MagicDefense = 25
	hat.MPBonus = 30
	hat.RequiredLevel = 10
	hat.RequiredJob = game.JobWizard
	wizard.EquipItem(hat)

	return wizard
}

// displayPlayerStatus 显示玩家状态
func displayPlayerStatus(player *game.Player) {
	equipAttrs := player.GetTotalEquipmentAttributes()

	fmt.Printf("玩家: %s (等级%d)\n", player.CharName, player.Level)
	fmt.Printf("  基础属性: 攻击%d 防御%d 魔攻%d 魔防%d\n",
		player.Attack, player.Defense, player.MagicAttack, player.MagicDefense)
	fmt.Printf("  装备加成: 攻击+%d 防御+%d 魔攻+%d 魔防+%d\n",
		equipAttrs.Attack, equipAttrs.Defense, equipAttrs.MagicAttack, equipAttrs.MagicDefense)
	fmt.Printf("  总属性: 攻击%d 防御%d 魔攻%d 魔防%d\n",
		player.Attack+equipAttrs.Attack, player.Defense+equipAttrs.Defense,
		player.MagicAttack+equipAttrs.MagicAttack, player.MagicDefense+equipAttrs.MagicDefense)
	fmt.Printf("  生命值: %d/%d 魔法值: %d/%d\n", player.HP, player.MaxHP, player.MP, player.MaxMP)

	// 显示装备信息
	fmt.Printf("  已装备物品:\n")
	allEquipped := player.GetEquipment().GetAllEquippedItems()
	for pos, equipment := range allEquipped {
		fmt.Printf("    位置%d: %s (耐久%d/%d)\n",
			pos, equipment.Name, equipment.Durability, equipment.MaxDurability)
	}
	fmt.Println()
}

// demonstrateEquipmentSystem 演示装备系统
func demonstrateEquipmentSystem(player *game.Player) {
	fmt.Printf("为 %s 演示装备系统:\n", player.CharName)

	// 创建一个新戒指
	ring := game.NewEquipment(3001, "力量戒指", game.EquipTypeRing, game.QualityUncommon)
	ring.Attack = 15
	ring.CriticalRate = 5
	ring.RequiredLevel = 12
	ring.RequiredJob = game.JobAll

	fmt.Printf("尝试装备: %s\n", ring.Name)

	// 装备戒指
	if err := player.EquipItem(ring); err != nil {
		fmt.Printf("装备失败: %v\n", err)
	} else {
		fmt.Printf("成功装备 %s\n", ring.Name)

		// 显示属性变化
		equipAttrs := player.GetTotalEquipmentAttributes()
		fmt.Printf("装备后总攻击力: %d (基础%d + 装备%d)\n",
			player.Attack+equipAttrs.Attack, player.Attack, equipAttrs.Attack)
		fmt.Printf("暴击率: %d%%\n", equipAttrs.CriticalRate)
	}

	// 尝试装备第二个戒指
	ring2 := game.NewEquipment(3002, "敏捷戒指", game.EquipTypeRing, game.QualityCommon)
	ring2.Agility = 12
	ring2.Accuracy = 8
	ring2.RequiredLevel = 10
	ring2.RequiredJob = game.JobAll

	fmt.Printf("\n尝试装备第二个戒指: %s\n", ring2.Name)
	if err := player.EquipItem(ring2); err != nil {
		fmt.Printf("装备失败: %v\n", err)
	} else {
		fmt.Printf("成功装备 %s\n", ring2.Name)
	}
}

// demonstrateCombatWithEquipment 演示装备对战斗的影响
func demonstrateCombatWithEquipment(warrior, wizard *game.Player) {
	fmt.Printf("%s 攻击 %s (装备影响战斗)\n", warrior.CharName, wizard.CharName)

	// 创建战斗系统
	combatSystem := game.NewCombatSystem()

	// 记录战斗前的状态
	wizardHPBefore := wizard.HP

	// 进行攻击
	result, err := combatSystem.ProcessAttack(warrior, wizard, 0)
	if err != nil {
		fmt.Printf("攻击失败: %v\n", err)
		return
	}

	fmt.Printf("攻击结果: 造成 %d 点伤害\n", result.Damage)
	fmt.Printf("%s 生命值: %d -> %d\n", wizard.CharName, wizardHPBefore, wizard.HP)

	// 检查装备耐久度
	if weapon, exists := warrior.GetEquippedItem(game.EquipPosWeapon); exists {
		fmt.Printf("%s 武器耐久度: %d/%d\n", warrior.CharName, weapon.Durability, weapon.MaxDurability)
	}
}

// demonstrateDurabilitySystem 演示耐久度系统
func demonstrateDurabilitySystem(player *game.Player) {
	fmt.Printf("为 %s 演示耐久度系统:\n", player.CharName)

	if weapon, exists := player.GetEquippedItem(game.EquipPosWeapon); exists {
		fmt.Printf("武器 %s 当前耐久度: %d/%d\n", weapon.Name, weapon.Durability, weapon.MaxDurability)

		// 模拟装备受损
		weapon.TakeDamage(20)
		fmt.Printf("受损后耐久度: %d/%d\n", weapon.Durability, weapon.MaxDurability)

		// 修理装备
		weapon.Repair()
		fmt.Printf("修理后耐久度: %d/%d\n", weapon.Durability, weapon.MaxDurability)
	}
}

// demonstrateEnhancementSystem 演示强化系统
func demonstrateEnhancementSystem(player *game.Player) {
	fmt.Printf("为 %s 演示强化系统:\n", player.CharName)

	if weapon, exists := player.GetEquippedItem(game.EquipPosWeapon); exists {
		fmt.Printf("武器 %s 当前强化等级: +%d\n", weapon.Name, weapon.EnhanceLevel)
		fmt.Printf("当前攻击力: %d\n", weapon.Attack)

		// 强化装备
		if weapon.Enhance() {
			fmt.Printf("强化成功! 新等级: +%d\n", weapon.EnhanceLevel)
			fmt.Printf("强化后攻击力: %d\n", weapon.Attack)

			// 显示总属性变化
			equipAttrs := player.GetTotalEquipmentAttributes()
			fmt.Printf("玩家总攻击力: %d\n", player.Attack+equipAttrs.Attack)
		} else {
			fmt.Printf("强化失败!\n")
		}
	}
}
