package game

import (
	"testing"
	"time"
)

// TestEquipmentCreation 测试装备创建
func TestEquipmentCreation(t *testing.T) {
	equipment := NewEquipment(1001, "屠龙刀", EquipTypeWeapon, QualityLegendary)
	
	if equipment.ItemID != 1001 {
		t.<PERSON><PERSON><PERSON>("Expected ItemID 1001, got %d", equipment.ItemID)
	}
	
	if equipment.Name != "屠龙刀" {
		t.<PERSON><PERSON>("Expected name '屠龙刀', got %s", equipment.Name)
	}
	
	if equipment.Type != EquipTypeWeapon {
		t.<PERSON><PERSON><PERSON>("Expected type EquipTypeWeapon, got %d", equipment.Type)
	}
	
	if equipment.Quality != QualityLegendary {
		t.Errorf("Expected quality QualityLegendary, got %d", equipment.Quality)
	}
	
	if equipment.Durability != 100 {
		t.Errorf("Expected durability 100, got %d", equipment.Durability)
	}
}

// TestEquipmentCanEquip 测试装备条件检查
func TestEquipmentCanEquip(t *testing.T) {
	// 创建测试玩家
	player := NewPlayer("test", "测试战士", JobWarrior, GenderMale)
	player.Level = 10
	
	// 创建装备
	equipment := NewEquipment(1001, "新手剑", EquipTypeWeapon, QualityCommon)
	equipment.RequiredLevel = 5
	equipment.RequiredJob = JobWarrior
	equipment.RequiredGender = GenderAll
	
	// 测试正常装备
	if err := equipment.CanEquip(player); err != nil {
		t.Errorf("Should be able to equip, but got error: %v", err)
	}
	
	// 测试等级不足
	equipment.RequiredLevel = 15
	if err := equipment.CanEquip(player); err == nil {
		t.Error("Should not be able to equip due to level requirement")
	}
	equipment.RequiredLevel = 5
	
	// 测试职业不符
	equipment.RequiredJob = JobWizard
	if err := equipment.CanEquip(player); err == nil {
		t.Error("Should not be able to equip due to job requirement")
	}
	equipment.RequiredJob = JobWarrior
	
	// 测试装备损坏
	equipment.Durability = 0
	if err := equipment.CanEquip(player); err == nil {
		t.Error("Should not be able to equip broken equipment")
	}
}

// TestEquipmentManager 测试装备管理器
func TestEquipmentManager(t *testing.T) {
	// 创建测试玩家
	player := NewPlayer("test", "测试战士", JobWarrior, GenderMale)
	player.Level = 10
	
	// 创建装备
	weapon := NewEquipment(1001, "新手剑", EquipTypeWeapon, QualityCommon)
	weapon.Attack = 20
	weapon.RequiredLevel = 5
	weapon.RequiredJob = JobWarrior
	
	helmet := NewEquipment(1002, "新手头盔", EquipTypeHelmet, QualityCommon)
	helmet.Defense = 15
	helmet.RequiredLevel = 5
	helmet.RequiredJob = JobWarrior
	
	// 测试装备武器
	if err := player.EquipItem(weapon); err != nil {
		t.Errorf("Failed to equip weapon: %v", err)
	}
	
	// 验证武器已装备
	if equippedWeapon, exists := player.GetEquippedItem(EquipPosWeapon); !exists {
		t.Error("Weapon should be equipped")
	} else if equippedWeapon.Name != "新手剑" {
		t.Errorf("Expected weapon '新手剑', got %s", equippedWeapon.Name)
	}
	
	// 测试装备头盔
	if err := player.EquipItem(helmet); err != nil {
		t.Errorf("Failed to equip helmet: %v", err)
	}
	
	// 验证头盔已装备
	if equippedHelmet, exists := player.GetEquippedItem(EquipPosHelmet); !exists {
		t.Error("Helmet should be equipped")
	} else if equippedHelmet.Name != "新手头盔" {
		t.Errorf("Expected helmet '新手头盔', got %s", equippedHelmet.Name)
	}
	
	// 测试属性加成
	attrs := player.GetTotalEquipmentAttributes()
	if attrs.Attack != 20 {
		t.Errorf("Expected attack bonus 20, got %d", attrs.Attack)
	}
	if attrs.Defense != 15 {
		t.Errorf("Expected defense bonus 15, got %d", attrs.Defense)
	}
}

// TestEquipmentDurability 测试装备耐久度
func TestEquipmentDurability(t *testing.T) {
	equipment := NewEquipment(1001, "测试剑", EquipTypeWeapon, QualityCommon)
	
	// 测试初始耐久度
	if equipment.Durability != 100 {
		t.Errorf("Expected initial durability 100, got %d", equipment.Durability)
	}
	
	if equipment.IsBroken() {
		t.Error("New equipment should not be broken")
	}
	
	// 测试受损
	equipment.TakeDamage(30)
	if equipment.Durability != 70 {
		t.Errorf("Expected durability 70 after damage, got %d", equipment.Durability)
	}
	
	// 测试完全损坏
	equipment.TakeDamage(80)
	if equipment.Durability != 0 {
		t.Errorf("Expected durability 0 after heavy damage, got %d", equipment.Durability)
	}
	
	if !equipment.IsBroken() {
		t.Error("Equipment should be broken")
	}
	
	// 测试修理
	equipment.Repair()
	if equipment.Durability != equipment.MaxDurability {
		t.Errorf("Expected durability %d after repair, got %d", equipment.MaxDurability, equipment.Durability)
	}
	
	if equipment.IsBroken() {
		t.Error("Repaired equipment should not be broken")
	}
}

// TestEquipmentEnhancement 测试装备强化
func TestEquipmentEnhancement(t *testing.T) {
	equipment := NewEquipment(1001, "测试剑", EquipTypeWeapon, QualityCommon)
	equipment.Attack = 50
	equipment.Defense = 20
	
	originalAttack := equipment.Attack
	originalDefense := equipment.Defense
	
	// 测试强化
	success := equipment.Enhance()
	if !success {
		t.Error("Enhancement should succeed for low level equipment")
	}
	
	if equipment.EnhanceLevel != 1 {
		t.Errorf("Expected enhance level 1, got %d", equipment.EnhanceLevel)
	}
	
	// 验证属性提升
	if equipment.Attack <= originalAttack {
		t.Error("Attack should increase after enhancement")
	}
	
	if equipment.Defense <= originalDefense {
		t.Error("Defense should increase after enhancement")
	}
}

// TestEquipmentGems 测试宝石镶嵌
func TestEquipmentGems(t *testing.T) {
	equipment := NewEquipment(1001, "测试剑", EquipTypeWeapon, QualityCommon)
	
	// 创建宝石
	gem := &Gem{
		ID:   2001,
		Type: GemTypeAttack,
		Level: 1,
		Attributes: map[string]int32{
			"attack": 10,
		},
	}
	
	// 测试镶嵌宝石
	if err := equipment.AddGem(0, gem); err != nil {
		t.Errorf("Failed to add gem: %v", err)
	}
	
	// 测试重复镶嵌
	if err := equipment.AddGem(0, gem); err == nil {
		t.Error("Should not be able to add gem to occupied position")
	}
	
	// 测试无效位置
	if err := equipment.AddGem(10, gem); err == nil {
		t.Error("Should not be able to add gem to invalid position")
	}
	
	// 测试属性加成
	attrs := equipment.GetTotalAttributes()
	if attrs.Attack != equipment.Attack+10 {
		t.Errorf("Expected attack %d with gem bonus, got %d", equipment.Attack+10, attrs.Attack)
	}
	
	// 测试移除宝石
	removedGem := equipment.RemoveGem(0)
	if removedGem == nil {
		t.Error("Should return removed gem")
	}
	if removedGem.ID != gem.ID {
		t.Errorf("Expected gem ID %d, got %d", gem.ID, removedGem.ID)
	}
	
	// 验证属性恢复
	attrs = equipment.GetTotalAttributes()
	if attrs.Attack != equipment.Attack {
		t.Errorf("Expected attack %d after gem removal, got %d", equipment.Attack, attrs.Attack)
	}
}

// TestEquipmentBinding 测试装备绑定
func TestEquipmentBinding(t *testing.T) {
	equipment := NewEquipment(1001, "测试剑", EquipTypeWeapon, QualityCommon)
	
	// 测试初始状态
	if equipment.IsBound {
		t.Error("New equipment should not be bound")
	}
	
	// 测试绑定
	playerID := uint64(12345)
	equipment.Bind(playerID)
	
	if !equipment.IsBound {
		t.Error("Equipment should be bound after binding")
	}
	
	if equipment.OwnerID != playerID {
		t.Errorf("Expected owner ID %d, got %d", playerID, equipment.OwnerID)
	}
	
	if equipment.BindTime.IsZero() {
		t.Error("Bind time should be set")
	}
}

// TestEquipmentExpiration 测试装备过期
func TestEquipmentExpiration(t *testing.T) {
	player := NewPlayer("test", "测试战士", JobWarrior, GenderMale)
	player.Level = 10
	
	equipment := NewEquipment(1001, "限时剑", EquipTypeWeapon, QualityCommon)
	equipment.RequiredLevel = 5
	equipment.RequiredJob = JobWarrior
	
	// 设置过期时间为过去
	pastTime := time.Now().Add(-time.Hour)
	equipment.ExpireTime = &pastTime
	
	// 测试过期装备不能装备
	if err := equipment.CanEquip(player); err == nil {
		t.Error("Should not be able to equip expired equipment")
	}
	
	// 设置过期时间为未来
	futureTime := time.Now().Add(time.Hour)
	equipment.ExpireTime = &futureTime
	
	// 测试未过期装备可以装备
	if err := equipment.CanEquip(player); err != nil {
		t.Errorf("Should be able to equip non-expired equipment: %v", err)
	}
}
