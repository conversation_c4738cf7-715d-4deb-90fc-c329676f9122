package protocol

import (
	"encoding/binary"
	"fmt"
	"io"
)

// Buffer 消息缓冲区，用于序列化和反序列化
type Buffer struct {
	data []byte
	pos  int
}

// NewBuffer 创建新的缓冲区
func NewBuffer() *Buffer {
	return &Buffer{
		data: make([]byte, 0, 1024),
		pos:  0,
	}
}

// NewBufferFromBytes 从字节数组创建缓冲区
func NewBufferFromBytes(data []byte) *Buffer {
	return &Buffer{
		data: data,
		pos:  0,
	}
}

// Bytes 获取缓冲区数据
func (b *Buffer) Bytes() []byte {
	return b.data
}

// Len 获取数据长度
func (b *Buffer) Len() int {
	return len(b.data)
}

// Remaining 获取剩余可读字节数
func (b *Buffer) Remaining() int {
	return len(b.data) - b.pos
}

// Reset 重置缓冲区
func (b *Buffer) Reset() {
	b.data = b.data[:0]
	b.pos = 0
}

// WriteInt8 写入8位整数
func (b *Buffer) WriteInt8(v int8) {
	b.data = append(b.data, byte(v))
}

// WriteUint8 写入8位无符号整数
func (b *Buffer) WriteUint8(v uint8) {
	b.data = append(b.data, v)
}

// WriteInt16 写入16位整数（小端序）
func (b *Buffer) WriteInt16(v int16) {
	buf := make([]byte, 2)
	binary.LittleEndian.PutUint16(buf, uint16(v))
	b.data = append(b.data, buf...)
}

// WriteUint16 写入16位无符号整数（小端序）
func (b *Buffer) WriteUint16(v uint16) {
	buf := make([]byte, 2)
	binary.LittleEndian.PutUint16(buf, v)
	b.data = append(b.data, buf...)
}

// WriteInt32 写入32位整数（小端序）
func (b *Buffer) WriteInt32(v int32) {
	buf := make([]byte, 4)
	binary.LittleEndian.PutUint32(buf, uint32(v))
	b.data = append(b.data, buf...)
}

// WriteUint32 写入32位无符号整数（小端序）
func (b *Buffer) WriteUint32(v uint32) {
	buf := make([]byte, 4)
	binary.LittleEndian.PutUint32(buf, v)
	b.data = append(b.data, buf...)
}

// WriteInt64 写入64位整数（小端序）
func (b *Buffer) WriteInt64(v int64) {
	buf := make([]byte, 8)
	binary.LittleEndian.PutUint64(buf, uint64(v))
	b.data = append(b.data, buf...)
}

// WriteUint64 写入64位无符号整数（小端序）
func (b *Buffer) WriteUint64(v uint64) {
	buf := make([]byte, 8)
	binary.LittleEndian.PutUint64(buf, v)
	b.data = append(b.data, buf...)
}

// WriteString 写入字符串（长度+内容）
func (b *Buffer) WriteString(s string) {
	data := []byte(s)
	b.WriteUint16(uint16(len(data)))
	b.data = append(b.data, data...)
}

// WriteBytes 写入字节数组（长度+内容）
func (b *Buffer) WriteBytes(data []byte) {
	b.WriteUint32(uint32(len(data)))
	b.data = append(b.data, data...)
}

// WriteBool 写入布尔值
func (b *Buffer) WriteBool(v bool) {
	if v {
		b.WriteUint8(1)
	} else {
		b.WriteUint8(0)
	}
}

// ReadInt8 读取8位整数
func (b *Buffer) ReadInt8() (int8, error) {
	if b.pos >= len(b.data) {
		return 0, io.EOF
	}
	v := int8(b.data[b.pos])
	b.pos++
	return v, nil
}

// ReadUint8 读取8位无符号整数
func (b *Buffer) ReadUint8() (uint8, error) {
	if b.pos >= len(b.data) {
		return 0, io.EOF
	}
	v := b.data[b.pos]
	b.pos++
	return v, nil
}

// ReadInt16 读取16位整数（小端序）
func (b *Buffer) ReadInt16() (int16, error) {
	if b.pos+2 > len(b.data) {
		return 0, io.EOF
	}
	v := int16(binary.LittleEndian.Uint16(b.data[b.pos:]))
	b.pos += 2
	return v, nil
}

// ReadUint16 读取16位无符号整数（小端序）
func (b *Buffer) ReadUint16() (uint16, error) {
	if b.pos+2 > len(b.data) {
		return 0, io.EOF
	}
	v := binary.LittleEndian.Uint16(b.data[b.pos:])
	b.pos += 2
	return v, nil
}

// ReadInt32 读取32位整数（小端序）
func (b *Buffer) ReadInt32() (int32, error) {
	if b.pos+4 > len(b.data) {
		return 0, io.EOF
	}
	v := int32(binary.LittleEndian.Uint32(b.data[b.pos:]))
	b.pos += 4
	return v, nil
}

// ReadUint32 读取32位无符号整数（小端序）
func (b *Buffer) ReadUint32() (uint32, error) {
	if b.pos+4 > len(b.data) {
		return 0, io.EOF
	}
	v := binary.LittleEndian.Uint32(b.data[b.pos:])
	b.pos += 4
	return v, nil
}

// ReadInt64 读取64位整数（小端序）
func (b *Buffer) ReadInt64() (int64, error) {
	if b.pos+8 > len(b.data) {
		return 0, io.EOF
	}
	v := int64(binary.LittleEndian.Uint64(b.data[b.pos:]))
	b.pos += 8
	return v, nil
}

// ReadUint64 读取64位无符号整数（小端序）
func (b *Buffer) ReadUint64() (uint64, error) {
	if b.pos+8 > len(b.data) {
		return 0, io.EOF
	}
	v := binary.LittleEndian.Uint64(b.data[b.pos:])
	b.pos += 8
	return v, nil
}

// ReadString 读取字符串（长度+内容）
func (b *Buffer) ReadString() (string, error) {
	length, err := b.ReadUint16()
	if err != nil {
		return "", err
	}
	
	if b.pos+int(length) > len(b.data) {
		return "", io.EOF
	}
	
	data := b.data[b.pos : b.pos+int(length)]
	b.pos += int(length)
	return string(data), nil
}

// ReadBytes 读取字节数组（长度+内容）
func (b *Buffer) ReadBytes() ([]byte, error) {
	length, err := b.ReadUint32()
	if err != nil {
		return nil, err
	}
	
	if b.pos+int(length) > len(b.data) {
		return nil, io.EOF
	}
	
	data := make([]byte, length)
	copy(data, b.data[b.pos:b.pos+int(length)])
	b.pos += int(length)
	return data, nil
}

// ReadBool 读取布尔值
func (b *Buffer) ReadBool() (bool, error) {
	v, err := b.ReadUint8()
	if err != nil {
		return false, err
	}
	return v != 0, nil
}

// Skip 跳过指定字节数
func (b *Buffer) Skip(n int) error {
	if b.pos+n > len(b.data) {
		return io.EOF
	}
	b.pos += n
	return nil
}

// Seek 设置读取位置
func (b *Buffer) Seek(pos int) error {
	if pos < 0 || pos > len(b.data) {
		return fmt.Errorf("invalid position: %d", pos)
	}
	b.pos = pos
	return nil
}

// Position 获取当前读取位置
func (b *Buffer) Position() int {
	return b.pos
}
