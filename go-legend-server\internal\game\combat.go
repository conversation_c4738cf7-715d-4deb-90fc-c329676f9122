package game

import (
	"fmt"
	"math"
	"math/rand"
	"time"

	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// CombatSystem 战斗系统
type CombatSystem struct {
	logger *zap.Logger
}

// NewCombatSystem 创建战斗系统
func NewCombatSystem() *CombatSystem {
	return &CombatSystem{
		logger: logger.GetLogger(),
	}
}

// ProcessAttack 处理攻击
func (cs *CombatSystem) ProcessAttack(attacker, target *Player, skillID int32) (*CombatResult, error) {
	// 验证攻击条件
	if err := cs.validateAttack(attacker, target); err != nil {
		return nil, err
	}

	// 计算命中
	if !cs.calculateHit(attacker, target) {
		return &CombatResult{
			AttackerID: attacker.ID,
			TargetID:   target.ID,
			SkillID:    skillID,
			Damage:     0,
			IsMiss:     true,
		}, nil
	}

	// 计算伤害
	damage := cs.calculateDamage(attacker, target, skillID)

	// 应用伤害
	isDead := target.TakeDamage(damage)

	// 装备耐久度损耗
	cs.processEquipmentDurability(attacker, target)

	// 处理PK值
	cs.processPKValue(attacker, target)

	// 处理经验奖励
	if isDead {
		cs.processKillReward(attacker, target)
	}

	result := &CombatResult{
		AttackerID: attacker.ID,
		TargetID:   target.ID,
		SkillID:    skillID,
		Damage:     damage,
		IsDead:     isDead,
		IsMiss:     false,
	}

	cs.logger.Info("Combat processed",
		zap.Uint64("attacker_id", attacker.ID),
		zap.Uint64("target_id", target.ID),
		zap.Int32("skill_id", skillID),
		zap.Int32("damage", damage),
		zap.Bool("is_dead", isDead))

	return result, nil
}

// ProcessAttackMonster 处理攻击怪物
func (cs *CombatSystem) ProcessAttackMonster(attacker *Player, target *Monster, skillID int32) (*CombatResult, error) {
	// 验证攻击条件
	if !attacker.IsAlive() {
		return nil, fmt.Errorf("attacker is dead")
	}

	if !target.IsAlive() {
		return nil, fmt.Errorf("target is dead")
	}

	// 计算命中
	if !cs.calculateHitMonster(attacker, target) {
		return &CombatResult{
			AttackerID: attacker.ID,
			TargetID:   uint64(target.ID),
			SkillID:    skillID,
			Damage:     0,
			IsMiss:     true,
		}, nil
	}

	// 计算伤害
	damage := cs.calculateDamageToMonster(attacker, target, skillID)

	// 应用伤害
	isDead := target.TakeDamage(damage, attacker)

	// 装备耐久度损耗
	cs.processEquipmentDurabilityMonster(attacker)

	result := &CombatResult{
		AttackerID: attacker.ID,
		TargetID:   uint64(target.ID),
		SkillID:    skillID,
		Damage:     damage,
		IsDead:     isDead,
		IsMiss:     false,
		Timestamp:  time.Now().Unix(),
	}

	cs.logger.Debug("Player attacked monster",
		zap.Uint64("attacker_id", attacker.ID),
		zap.Int32("target_id", target.ID),
		zap.Int32("damage", damage),
		zap.Bool("target_dead", isDead))

	return result, nil
}

// calculateHitMonster 计算对怪物的命中
func (cs *CombatSystem) calculateHitMonster(attacker *Player, target *Monster) bool {
	// 获取装备属性加成
	equipmentAttrs := attacker.GetTotalEquipmentAttributes()

	// 计算命中率
	attackerAccuracy := attacker.Accuracy + equipmentAttrs.Accuracy
	targetAgility := target.Agility

	hitRate := float64(attackerAccuracy) / float64(attackerAccuracy+targetAgility)

	// 基础命中率不低于10%，不高于95%
	if hitRate < 0.1 {
		hitRate = 0.1
	} else if hitRate > 0.95 {
		hitRate = 0.95
	}

	return rand.Float64() < hitRate
}

// calculateDamageToMonster 计算对怪物的伤害
func (cs *CombatSystem) calculateDamageToMonster(attacker *Player, target *Monster, skillID int32) int32 {
	// 获取装备属性加成
	equipmentAttrs := attacker.GetTotalEquipmentAttributes()

	// 基础攻击力
	baseAttack := attacker.Attack + equipmentAttrs.Attack

	// 随机伤害浮动 (80%-120%)
	damageMultiplier := 0.8 + rand.Float64()*0.4
	damage := float64(baseAttack) * damageMultiplier

	// 防御减免
	defense := target.Defense
	damageReduction := float64(defense) / (float64(defense) + 100.0)
	damage *= (1.0 - damageReduction)

	// 最小伤害为1
	finalDamage := int32(math.Max(1, damage))

	return finalDamage
}

// processEquipmentDurabilityMonster 处理攻击怪物时的装备耐久度损耗
func (cs *CombatSystem) processEquipmentDurabilityMonster(attacker *Player) {
	// 攻击者武器耐久度损耗
	if weapon, exists := attacker.GetEquippedItem(EquipPosWeapon); exists {
		weapon.TakeDamage(1) // 每次攻击武器损耗1点耐久
		if weapon.IsBroken() {
			cs.logger.Warn("Weapon broken during combat",
				zap.String("player", attacker.CharName),
				zap.String("weapon", weapon.Name))
		}
	}
}

// validateAttack 验证攻击条件
func (cs *CombatSystem) validateAttack(attacker, target *Player) error {
	if !attacker.IsAlive() {
		return fmt.Errorf("attacker is dead")
	}

	if !target.IsAlive() {
		return fmt.Errorf("target is dead")
	}

	if attacker.ID == target.ID {
		return fmt.Errorf("cannot attack self")
	}

	// 检查距离
	if !cs.isInRange(attacker, target, 3) {
		return fmt.Errorf("target out of range")
	}

	// 检查PK规则
	if !cs.canAttack(attacker, target) {
		return fmt.Errorf("cannot attack this target")
	}

	return nil
}

// calculateHit 计算命中
func (cs *CombatSystem) calculateHit(attacker, target *Player) bool {
	// 获取装备属性加成
	attackerEquipAttrs := attacker.GetTotalEquipmentAttributes()
	targetEquipAttrs := target.GetTotalEquipmentAttributes()

	// 计算总精确度和敏捷度
	totalAccuracy := attacker.Accuracy + attackerEquipAttrs.Accuracy
	totalAgility := target.Agility + targetEquipAttrs.Agility

	// 基础命中率
	hitRate := float64(totalAccuracy) / (float64(totalAccuracy) + float64(totalAgility))

	// 最低命中率5%，最高95%
	if hitRate < 0.05 {
		hitRate = 0.05
	} else if hitRate > 0.95 {
		hitRate = 0.95
	}

	return rand.Float64() < hitRate
}

// processEquipmentDurability 处理装备耐久度损耗
func (cs *CombatSystem) processEquipmentDurability(attacker, target *Player) {
	// 攻击者武器耐久度损耗
	if weapon, exists := attacker.GetEquippedItem(EquipPosWeapon); exists {
		weapon.TakeDamage(1) // 每次攻击武器损耗1点耐久
		if weapon.IsBroken() {
			logger.Warn("Weapon broken during combat",
				zap.String("player", attacker.CharName),
				zap.String("weapon", weapon.Name))
		}
	}

	// 被攻击者装备耐久度损耗（随机选择一件装备）
	if rand.Intn(100) < 20 { // 20%概率装备受损
		target.DamageEquipment(1)
	}
}

// calculateDamage 计算伤害
func (cs *CombatSystem) calculateDamage(attacker, target *Player, skillID int32) int32 {
	var baseDamage int32
	var defense int32

	// 获取装备属性加成
	attackerEquipAttrs := attacker.GetTotalEquipmentAttributes()
	targetEquipAttrs := target.GetTotalEquipmentAttributes()

	if skillID == 0 {
		// 普通攻击
		baseDamage = attacker.Attack + attackerEquipAttrs.Attack
		defense = target.Defense + targetEquipAttrs.Defense
	} else {
		// 技能攻击
		skill, exists := attacker.skills.GetSkill(skillID)
		if exists {
			baseDamage = attacker.MagicAttack + attackerEquipAttrs.MagicAttack + skill.Damage
			defense = target.MagicDefense + targetEquipAttrs.MagicDefense
		} else {
			baseDamage = attacker.Attack + attackerEquipAttrs.Attack
			defense = target.Defense + targetEquipAttrs.Defense
		}
	}

	// 暴击计算
	criticalRate := attackerEquipAttrs.CriticalRate
	isCritical := false
	if rand.Intn(100) < int(criticalRate) {
		criticalMultiplier := 1.5 + float64(attackerEquipAttrs.CriticalDamage)/100.0
		baseDamage = int32(float64(baseDamage) * criticalMultiplier)
		isCritical = true
		logger.Debug("Critical hit!",
			zap.String("attacker", attacker.CharName),
			zap.Int32("damage", baseDamage))
	}

	// 随机浮动 ±20%
	variation := float64(baseDamage) * 0.2
	baseDamage += int32(rand.Float64()*variation*2 - variation)

	// 防御减免
	damage := baseDamage - defense/2

	// 最小伤害为1
	if damage < 1 {
		damage = 1
	}

	// 记录详细伤害信息
	logger.Debug("Damage calculation",
		zap.String("attacker", attacker.CharName),
		zap.String("target", target.CharName),
		zap.Int32("base_damage", baseDamage),
		zap.Int32("defense", defense),
		zap.Int32("final_damage", damage),
		zap.Bool("critical", isCritical))

	return damage
}

// isInRange 检查是否在攻击范围内
func (cs *CombatSystem) isInRange(attacker, target *Player, maxRange int32) bool {
	if attacker.MapID != target.MapID {
		return false
	}

	dx := float64(attacker.X - target.X)
	dy := float64(attacker.Y - target.Y)
	distance := math.Sqrt(dx*dx + dy*dy)

	return distance <= float64(maxRange)
}

// canAttack 检查是否可以攻击
func (cs *CombatSystem) canAttack(attacker, target *Player) bool {
	// 和平模式不能攻击玩家
	if attacker.AttackMode == AttackModePeace {
		return false
	}

	// 等级限制
	if attacker.Level < 7 || target.Level < 7 {
		return false
	}

	// 同账号不能攻击
	if attacker.AccountID == target.AccountID {
		return false
	}

	return true
}

// processPKValue 处理PK值
func (cs *CombatSystem) processPKValue(attacker, target *Player) {
	// 攻击白名玩家增加PK值
	if target.PKValue < 400 && attacker.PKValue == 0 {
		attacker.AddPKValue(10)
	}
}

// processKillReward 处理击杀奖励
func (cs *CombatSystem) processKillReward(killer, victim *Player) {
	// 根据等级差计算经验奖励
	levelDiff := victim.Level - killer.Level
	baseExp := int64(victim.Level * 10)

	// 等级差调整
	if levelDiff > 0 {
		baseExp = int64(float64(baseExp) * (1.0 + float64(levelDiff)*0.1))
	} else if levelDiff < -5 {
		baseExp = int64(float64(baseExp) * 0.1) // 杀低级玩家经验很少
	}

	if baseExp > 0 {
		killer.AddExperience(baseExp)
	}

	cs.logger.Info("Kill reward processed",
		zap.Uint64("killer_id", killer.ID),
		zap.Uint64("victim_id", victim.ID),
		zap.Int64("exp_reward", baseExp))
}

// CombatResult 战斗结果
type CombatResult struct {
	AttackerID uint64 `json:"attacker_id"`
	TargetID   uint64 `json:"target_id"`
	SkillID    int32  `json:"skill_id"`
	Damage     int32  `json:"damage"`
	IsDead     bool   `json:"is_dead"`
	IsMiss     bool   `json:"is_miss"`
	Timestamp  int64  `json:"timestamp"`
}

// SkillSystem 技能系统
type SkillSystem struct {
	logger *zap.Logger
}

// NewSkillSystem 创建技能系统
func NewSkillSystem() *SkillSystem {
	return &SkillSystem{
		logger: logger.GetLogger(),
	}
}

// UseSkill 使用技能
func (ss *SkillSystem) UseSkill(player *Player, skillID int32, targetX, targetY int32, targetID uint64) error {
	// 获取技能
	skill, exists := player.skills.GetSkill(skillID)
	if !exists {
		return fmt.Errorf("skill not found")
	}

	// 检查冷却时间
	if time.Since(skill.LastUseTime) < time.Duration(skill.Cooldown)*time.Millisecond {
		return fmt.Errorf("skill is in cooldown")
	}

	// 检查魔法值
	if player.MP < skill.MPCost {
		return fmt.Errorf("not enough MP")
	}

	// 消耗魔法值
	player.MP -= skill.MPCost
	if player.MP < 0 {
		player.MP = 0
	}

	// 更新使用时间
	skill.LastUseTime = time.Now()

	// 增加技能经验
	ss.addSkillExperience(player, skillID, 1)

	ss.logger.Info("Skill used",
		zap.Uint64("player_id", player.ID),
		zap.Int32("skill_id", skillID),
		zap.Int32("target_x", targetX),
		zap.Int32("target_y", targetY),
		zap.Uint64("target_id", targetID))

	return nil
}

// addSkillExperience 增加技能经验
func (ss *SkillSystem) addSkillExperience(player *Player, skillID int32, exp int64) {
	skill, exists := player.skills.GetSkill(skillID)
	if !exists {
		return
	}

	skill.Experience += exp

	// 检查升级
	requiredExp := ss.getRequiredExperience(skill.Level)
	if skill.Experience >= requiredExp {
		skill.Level++
		skill.Experience -= requiredExp

		// 更新技能属性
		ss.updateSkillAttributes(skill)

		ss.logger.Info("Skill level up",
			zap.Uint64("player_id", player.ID),
			zap.Int32("skill_id", skillID),
			zap.Int32("new_level", skill.Level))
	}
}

// getRequiredExperience 获取技能升级所需经验
func (ss *SkillSystem) getRequiredExperience(level int32) int64 {
	return int64(level * level * 50)
}

// updateSkillAttributes 更新技能属性
func (ss *SkillSystem) updateSkillAttributes(skill *Skill) {
	// 根据技能等级更新属性
	skill.Damage = skill.Level * 10
	skill.MPCost = 5 + skill.Level*2
	skill.Range = 3 + skill.Level/5
	skill.Cooldown = 1000 - skill.Level*10
	if skill.Cooldown < 100 {
		skill.Cooldown = 100
	}
}
