package network

import (
	"context"
	"fmt"
	"net"
	"sync"
	"sync/atomic"

	"github.com/legend-server/go-legend-server/internal/config"
	"github.com/legend-server/go-legend-server/internal/protocol"
	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// Server TCP服务器
type Server struct {
	config    *config.ServerConfig
	listener  net.Listener
	sessions  sync.Map // map[uint64]*Session
	sessionID uint64
	running   int32
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	handler   MessageHandler
	logger    *zap.Logger
}

// MessageHandler 消息处理器接口
type MessageHandler interface {
	HandleMessage(session *Session, msg protocol.Message) error
	OnSessionConnected(session *Session) error
	OnSessionDisconnected(session *Session) error
}

// NewServer 创建新的TCP服务器
func NewServer(cfg *config.ServerConfig, handler MessageHandler) *Server {
	ctx, cancel := context.WithCancel(context.Background())

	return &Server{
		config:  cfg,
		ctx:     ctx,
		cancel:  cancel,
		handler: handler,
		logger:  logger.GetLogger(),
	}
}

// Start 启动服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", addr, err)
	}

	s.listener = listener
	atomic.StoreInt32(&s.running, 1)

	s.logger.Info("Server started",
		zap.String("address", addr),
		zap.String("name", s.config.Name),
		zap.Int("id", s.config.ID))

	// 启动接受连接的goroutine
	s.wg.Add(1)
	go s.acceptLoop()

	return nil
}

// Stop 停止服务器
func (s *Server) Stop() error {
	if !atomic.CompareAndSwapInt32(&s.running, 1, 0) {
		return nil
	}

	s.logger.Info("Stopping server...")

	// 关闭监听器
	if s.listener != nil {
		s.listener.Close()
	}

	// 取消上下文
	s.cancel()

	// 关闭所有会话
	s.sessions.Range(func(key, value interface{}) bool {
		if session, ok := value.(*Session); ok {
			session.Close()
		}
		return true
	})

	// 等待所有goroutine结束
	s.wg.Wait()

	s.logger.Info("Server stopped")
	return nil
}

// acceptLoop 接受连接循环
func (s *Server) acceptLoop() {
	defer s.wg.Done()

	for {
		conn, err := s.listener.Accept()
		if err != nil {
			if atomic.LoadInt32(&s.running) == 0 {
				return
			}
			s.logger.Error("Failed to accept connection", zap.Error(err))
			continue
		}

		// 检查连接数限制
		if s.GetSessionCount() >= s.config.MaxConnections {
			s.logger.Warn("Connection limit reached, rejecting connection",
				zap.String("remote", conn.RemoteAddr().String()))
			conn.Close()
			continue
		}

		// 创建新会话
		sessionID := atomic.AddUint64(&s.sessionID, 1)
		session := NewSession(sessionID, conn, s)

		// 存储会话
		s.sessions.Store(sessionID, session)

		// 启动会话处理
		s.wg.Add(1)
		go s.handleSession(session)
	}
}

// handleSession 处理会话
func (s *Server) handleSession(session *Session) {
	defer s.wg.Done()
	defer s.removeSession(session.ID())

	s.logger.Info("New session connected",
		zap.Uint64("session_id", session.ID()),
		zap.String("remote", session.RemoteAddr()))

	// 通知会话连接
	if err := s.handler.OnSessionConnected(session); err != nil {
		s.logger.Error("Failed to handle session connected",
			zap.Uint64("session_id", session.ID()),
			zap.Error(err))
		session.Close()
		return
	}

	// 启动会话
	if err := session.Start(s.ctx); err != nil {
		s.logger.Error("Failed to start session",
			zap.Uint64("session_id", session.ID()),
			zap.Error(err))
	}

	// 通知会话断开
	if err := s.handler.OnSessionDisconnected(session); err != nil {
		s.logger.Error("Failed to handle session disconnected",
			zap.Uint64("session_id", session.ID()),
			zap.Error(err))
	}

	s.logger.Info("Session disconnected",
		zap.Uint64("session_id", session.ID()))
}

// removeSession 移除会话
func (s *Server) removeSession(sessionID uint64) {
	s.sessions.Delete(sessionID)
}

// GetSession 获取会话
func (s *Server) GetSession(sessionID uint64) (*Session, bool) {
	if value, ok := s.sessions.Load(sessionID); ok {
		return value.(*Session), true
	}
	return nil, false
}

// GetSessionCount 获取会话数量
func (s *Server) GetSessionCount() int {
	count := 0
	s.sessions.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// BroadcastMessage 广播消息
func (s *Server) BroadcastMessage(msg protocol.Message) {
	data, err := protocol.PackMessage(msg)
	if err != nil {
		s.logger.Error("Failed to pack broadcast message", zap.Error(err))
		return
	}

	s.sessions.Range(func(key, value interface{}) bool {
		if session, ok := value.(*Session); ok {
			session.SendRaw(data)
		}
		return true
	})
}

// HandleMessage 处理消息（由Session调用）
func (s *Server) HandleMessage(session *Session, msg protocol.Message) error {
	return s.handler.HandleMessage(session, msg)
}

// IsRunning 检查服务器是否运行中
func (s *Server) IsRunning() bool {
	return atomic.LoadInt32(&s.running) == 1
}

// GetStats 获取服务器统计信息
func (s *Server) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"running":         s.IsRunning(),
		"session_count":   s.GetSessionCount(),
		"max_connections": s.config.MaxConnections,
		"server_name":     s.config.Name,
		"server_id":       s.config.ID,
	}
}
