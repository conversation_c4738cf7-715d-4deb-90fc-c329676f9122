# 游戏服务器配置文件

# 服务器配置
server:
  name: "热血传奇Go版"
  id: 1001
  host: "0.0.0.0"
  port: 7863
  read_timeout: "30s"
  write_timeout: "30s"
  max_connections: 10000
  auth_key: "#)(@((EDLL@PP330@LD@:<VDKSS@_#(((#D(833NN"

# 数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "123456"
  database: "legend_server"
  charset: "utf8mb4"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: "1h"

# Redis配置
redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  pool_size: 100
  min_idle_conns: 10
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

# 游戏配置
game:
  exp_multiplier: 1.0      # 经验倍率
  money_multiplier: 1.0    # 金币倍率
  drop_multiplier: 1.0     # 掉落倍率
  max_level: 100           # 最大等级
  start_map: "xinshou"     # 新手村地图
  start_x: 60              # 出生X坐标
  start_y: 112             # 出生Y坐标
  pk_enabled: true         # 是否开启PK
  guild_member_max: 100    # 公会最大成员数
  save_interval: "5m"      # 数据保存间隔

# 日志配置
log:
  level: "info"            # 日志级别: debug, info, warn, error
  format: "json"           # 日志格式: json, console
  output: "file"           # 输出方式: file, console, both
  filename: "logs/gameserver.log"
  max_size: 100            # 单个文件最大大小(MB)
  max_age: 30              # 文件保留天数
  max_backups: 10          # 最大备份文件数
  compress: true           # 是否压缩备份文件
