#pragma once

namespace OGSLib
{
	class GameServerLib;
	class ConstsString
	{
		typedef std::map<std::string,std::string> StringMap;
		StringMap mStringMap;
	public:
		ConstsString(void);
		virtual ~ConstsString(void);

		bool Load(GameServerLib* gslib);
		bool ReLoad(GameServerLib* gslib);
		bool LoadDefault();
		void dest();
		void reload_key(char* dest,std::string key);

		char NOTIFY_ITEM_LOCK_CAN_NOT_PICK_UP[512];
		char NOTIFY_ITEM_LEVEL_LIMINT_CAN_NOT_PICK_UP[512];
		char NOTIFY_BUY_ITEM_NOT_ENOPHY_GAMEMONEY[512];
		char NOTIFY_BUY_ITEM_NOT_ENOPHY_GAMEMONEYBIND[512];
		char NOTIFY_BUY_ITEM_NOT_ENOPHY_VCOIN[512];
		char NOTIFY_BUY_ITEM_NOT_ENOPHY_VCOINBIND[512];
		char NOTIFY_USE_TIEM_LIMIT_ERROR[512];
		char NOTIFY_ZHUANSHENG_LEVEL_LIMIT[512];
		//char NOTIFY_USE_ITEM_LIMIT_MAXLOAD[512];
		//char NOTIFY_USE_ITEM_LIMIT_MAXBRAWN[512];
		char NOTIFY_REPAIR_ITEM_NONEEDTOREPAIR[512];
		char NOTIFY_REPAIR_ITEM_NOT_ENOPHY_GAMEMONEY[512];
		char NOTIFY_NOT_ENOUPH_MP[512];
		char NOTIFY_ITEM_SLOB_FULL_CAN_NOT_PICK_UP[512];
		char NOTIFY_RELIVE_STAND_NOT_ENOUPH_VCOIN[512];
		char NOTIFY_DROP_ITEM_CAN_NOT_ON_MAP[512];
		char NOTIFY_DROP_ITEM_CAN_NOT_AUTH_TYPE[512];
		char NOTIFY_DROP_ITEM_CAN_NOT_BIND[512];
		char NOTIFY_TRADE_ITEM_CAN_NOT_BIND[512];
		char NOTIFY_CHAT_TOO_QUICK[512];
		char NOTIFY_PLAYER_NOT_ONLINE_NO_PRIVATE_CHAT[512];
		char NOTIFY_SKILL_YEMAN_FALIED[512];
		char NOTIFY_SKILL_CISHAJIANSHU_OPEN[512];
		char NOTIFY_SKILL_CISHAJIANSHU_CLOSE[512];
		char NOTIFY_SKILL_BANYUEWANDAO_OPEN[512];
		char NOTIFY_SKILL_BANYUEWANDAO_CLOSE[512];
		char NOTIFY_SKILL_LIEHUOJIANFA_OK[512];
		char NOTIFY_SKILL_COLDING[512];
		char NOTIFY_SKILL_SHUNJIANYIDONG_FAILED[512];
		char NOTIFY_MERDER[512];
		char NOTIFY_PK_KILLED[512];
		char NOTIFY_PK_MAP_NOT_MERDER[512];
		char NOTIFY_MAP_PK_PROHIBIT[512];
		char NOTIFY_MAPTIME_PK_PROHIBIT[512];
		char NOTIFY_TRADE_TOO_FAR[512];
		char NOTIFY_TRADE_CAN_NOT_AUTH_TYPE[512];
		char NOTIFY_TRADE_CAN_NOT_LEVEL[512];
		char NOTIFY_ITEM_USE_TIME_TOO_SHORT[512];
		char NOTIFY_BAG_USE_ITEM_MAP_DISABLED[512];
		char NOTIFY_SKILL_MAP_DISABLED[512];
		char NOTIFY_MERGESTEEL_SUC[512];
		char NOTIFY_MERGESTEEL_FAL[512];
		char NOTIFY_UPGRADEEQUIP_SUC[512];
		char NOTIFY_UPGRADEEQUIP_LASTTIME_LIMIT[512];
		char NOTIFY_QILING_FULL[512];
		char NOTIFY_QILING_SUC[512];
		char NOTIFY_UPGRADEEQUIP_FAL[512];
		char NOTIFY_NOENOUGHMONEY_FAL[512];
		char NOTIFY_UPGRADE_MATCHSTONE_FAL[512];
		char NOTIFY_UPGRADEEQUIP_FAL_NEED_LIMIT[512];
		char NOTIFY_BUY_ITEM_NOT_YOUKE[512];
		char NOTIFY_SKILL_YOULINGDUN[512];
		char NOTIFY_SKILL_SHENGZHANJIASHU[512];
		char NOTIFY_WORLD_CHAT_LEVEL_LIMIT[512];
		char NOTIFY_GROUP_CHAT_LEVEL_LIMIT[512];
		char NOTIFY_GUILD_CHAT_LEVEL_LIMIT[512];
		char NOTIFY_NORMAL_CHAT_LEVEL_LIMIT[512];
		char NOTIFY_MAP_CHAT_LEVEL_LIMIT[512];
		char NOTIFY_PRIVATE_CHAT_LEVEL_LIMIT[512];
		char NOTIFY_HORN_CHAT_LEVEL_LIMIT[512];
		char NOTIFY_LOGIN_ANOTHER_PLACE[512];
		char NOTIFY_FRESH_VCOIN_WAIT[512];
		char NOTIFY_KICK_BUY_GM[512];
		char NOTIFY_TALK_PROHIBITED[512];
		char NOTIFY_TALK_ALLOWED[512];
		char NOTIFY_PK_KILL[512];
		char NOTIFY_YOUKE_CAN_NOT_CREATE_GUILD[512];
		char NOTIFY_YOUKE_CAN_NOT_JOIN_GUILD[512];
		char NOTIFY_CONGZI_INFO[512];
		char NOTIFY_ITEM_BAG_FULL[512];
		char NOTIFY_BUY_ITEM_BAG_FULL[512];
		char NOTIFY_ADD_SLOT_NOT_ENOUPH_VCOIN[512];
		char NOTIFY_GROUP_CREATE_FAIL_ERROR_AUTH_TYPE[512];
		char NOTIFY_GROUP_JOIN_FAIL_ERROR_AUTH_TYPE[512];
		char NOTIFY_GROUP_MEMBER_ADD[512];
		char NOTIFY_JOIN_GROUP[512];
		char NOTIFY_GROUP_MEMBER_REM[512];
		char NOTIFY_LEAVE_GROUP[512];
		char NOTIFY_TRADE_FAIL[512];
		char NOTIFY_TRADE_SUC[512];
		char NOTIFY_TRADE_PLAYER_ITEM_DATA_ERROR[512];
		char NOTIFY_TRADE_PLAYER_OFFLINE[512];
		char NOTIFY_CHATTRADE_ADDITEM_SUC[512];
		char NOTIFY_NO_FRIEND_ONLINE[512];
		char NOTIFY_CHATTRADE_ADDITEM_FAIL[512];
		char NOTIFY_CHATTRADE_SUBITEM_SUC[512];
		char NOTIFY_CHATTRADE_SUBITEM_NOITEM[512];
		char NOTIFY_CHATTRADE_SUBITEM_FAIL[512];
		char NOTIFY_SLAVE_AI_OPEN[512];
		char NOTIFY_SLAVE_AI_CLOSE[512];
		char NOTIFY_GUILD_CREATE_EMPTY_NAME[512];
		char NOTIFY_GUILD_CREATE_TOO_LONG_NAME[512];
		char NOTIFY_GUILD_CREATE_NAME_PROHIBITED[512];
		char NOTIFY_GUILD_CREATE_EXIST_NAME[512];
		char NOTIFY_GUILD_CREATE_CONDITION_FAIL[512];
		char NOTIFY_GUILD_CREATE_AUTH_TYPE_ERROR[512];
		char NOTIFY_GUILD_CREATE_HAVE_GUILD[512];
		char NOTIFY_GUILD_JOIN_AUTH_TYPE_ERROR[512];
		char NOTIFY_GUILD_JOIN_HAVED[512];
		char NOTIFY_GUILD_JOIN_NOT_EXIST[512];
		char NOTIFY_GROUP_MEMBER_ONLINE[512];
		char NOTIFY_GROUP_MEMBER_OFFLINE[512];
		char NOTIFY_GUILD_MEMBER_ENTERING[512];
		char NOTIFY_GUILD_ADD_PRESIDENT[512];
		char NOTIFY_GUILD_SUB_PRESIDENT[512];
		char NOTIFY_GUILD_BECOME_ADMIN[512];
		char GUILD_RED_PACKET_MAIL_TITLE[512];
		char GUILD_RED_PACKET_MAIL_CONTENT[512];
		char NOTIFY_GUILD_ADV_FULL[512];
		char NOTIFY_MAIL_FULL[512];
		char NOTIFY_UPGRADEEQUIP_BREAK[512];
		char NOTIFY_SAFEAREA_ENTER[512];
		char NOTIFY_SAFEAREA_LEAVE[512];
		char NOTIFY_STATUS_NO_DAMAGE[512];
		char NOTIFY_STATUS_NO_DROP_ON_DIE[512];
		char NOTIFY_PK_LEVEL_LIMIT[512];
		char NOTIFY_IN_CHINA_LIMIT[512];
		char NOTIFY_GUILD_FULL[512];
		char NOTIFY_GUILD_WAR_START[512];
		char NOTIFY_RELIVE_STAND_NOT_ALLOW[512];
		char NOTIFY_GUILD_CREATE_LEVEL_LIMIT[512];
		char NOTIFY_GUILD_JOIN_LEVEL_LIMIT[512];
		char NOTIFY_SERVER_FULL[512];
		char NOTIFY_STEEL_EQUIP_NO_MONEY[512];
		char NOTIFY_STEEL_EQUIP_NO_BAG_ROOM[512];
		char NOTIFY_STEEL_EQUIP_NO_LEVEL[512];
		char NOTIFY_STEEL_EQUIP_SUC[512];
		char NOTIFY_ITEM_USE_BROKE[512];
		char NOTIFY_ITEM_USE_GENDER_ERROR[512];
		char NOTIFY_ITEM_USE_JOB_ERROR[512];
		char NOTIFY_ITEM_EXCHANGE_LV_LIMIT[512];

		char NOTIFY_HORN_HAVE_NO[512];
		char NOTIFY_WAR_KILL[512];
		char NOTIFY_WAR_KILL_RAM[512];
		char NOTIFY_NPC_SELL_DENY[512];
		char NOTIFY_GIFT[512];
		char NOTIFY_FRIEND_OPERATER_ERROR[512];
		char NOTIFY_FRIEND_AUTHTYPE_ERROR[512];
		char NOTIFY_FRIEND_AGREE_TIMEOUT_ERROR[512];
		char NOTIFY_FRIEND_TOO_MUCH[512];
		char NOTIFY_ENEMY_TOO_MUCH[512];
		char NOTIFY_BLACK_TOO_MUCH[512];
		char NOTIFY_FRIEND_DELETE[512];
		char NOTIFY_FRIEND_ADD[512];
		char NOTIFY_FRIEND_ADD_NOTIFY[512];
		char NOTIFY_CANCEL_BY_TRADING[512];
		char NOTIFY_EXCUPD_FROM_NO_LEVEL[512];
		char NOTIFY_EXCUPD_TO_HAVE_LEVEL[512];
		char NOTIFY_EXCUPD_BIND_TO_NOBIND[512];
		char NOTIFY_EXCUPD_EQUIP_TYPE_DIFF[512];
		char NOTIFY_EXCUPD_PROTECTITEM_LEVEL_TOO_LOW[512];
		char NOTIFY_EXCUPD_EQUIP_GM_NOT_ENOUPH[512];
		char NOTIFY_EXCUPD_EQUIP_BV_NOT_ENOUPH[512];
		char NOTIFY_ITEM_EXCHANGE_UPD_LEVEL_DOWN[512];
		char NOTIFY_PK_KILL_SUB_LUCK[512];
		//char NOTIFY_UPGRADEEQUIP_PUBLISH_SUC[512];
		//char NOTIFY_UPGRADEEQUIP_PUBLISH_FAIL[512];
		char NOTIFY_PROTECT_ITEM_NOT_ENOUPH_VCOIN[512];

		char NOTIFY_GAMEMONEY_ENOUGH[512];
		char NOTIFY_GAMEMONEYBIND_ENOUGH[512];
		char NOTIFY_VCOIN_ENOUGH[512];
		char NOTIFY_VCOINBIND_ENOUGH[512];

		//char NOTIFY_MONSTER_DROP_ITEM[512];
		//char NOTIFY_MONSTER_DROP_ITEM_NONAME[512];
		char NOTIFY_PK_VALUE_DESP[512];
		char NOTIFY_ADD_SLOT_MAX_LIMIT[512];
		char NOTIFY_ADD_FRIEND_NO_EXIST[512];
		char NOTIFY_CHECK_EQUIP_NO_EXIST[512];
		char NOTIFY_ITEM_TIME_LIMIT_FULL[512];
		char NOTIFY_CHATTRADE_SUC[512];
		char NOTIFY_MOXUESHI_EMPTY[512];
		char NOTIFY_MOXUESHI_UPGRADE_NO[512];

		char NOTIFY_DART_OUT_TIME[512];
		char NOTIFY_DART_OUT_RANGE_REMAIND[512];
		char NOTIFY_IS_DARTING[512];
		char NOTIFY_ACCEPT_DART_TASK[512];
		char NOTIFY_ROB_DART_NO_REWARD[512];
		char NOTIFY_ROB_DART_REWARD[512];
		char NOTIFY_ROB_DART_NUMBER_FULL[512];
		char NOTIFY_DART_DESTROYED[512];
		char NOTIFY_DART_DESTROYED_SUCCEED[512];
		char NOTIFY_DART_SUCCEED[512];
		char NOTIFY_DIRECTFLY_FORBID_DARTING[512];
		char NOTIFY_ACCEPT_DART_NUMBER_FULL[512];
		char NOTIFY_SKILL_FORBID_ROB_DART[512];
		char NOTIFY_DIRECT_FLY_MAP_FORBID[512];
		char NOTIFY_DIRECT_FLY_PKVALUE_FORBID[512];
		char NOTIFY_DIRECT_FLY_DEAD_FORBID[512];
		char NOTIFY_CDUPDITEM_VCOIN_FAIL[512];
		char NOTIFY_CANNOT_USE_LOTTERYDEPOT[512];
		char NOTIFY_BAG_AND_DEPOT_FULL[512];
		char NOTIFY_MAP_NO_INFO_PLAYER[512];
		char NOTIFY_MAP_NO_INFO_CHAT[512];
		char NOTIFY_BEYOND_ONSALE_LIMIT[512];
		char NOTIFY_BING_ITEM_LIMIT[512];
		char NOTIFY_ONSALE_TIME_ERROR[512];
		char NOTIFY_ONSALE_ITEM_MISS[512];
		char NOTIFY_ONSALE_MAX_LIMIT[512];
		char NOTIFY_ONSALE_OWNER_OFFLINE[512];
		char NOTIFY_ONSALE_MIN_LEVEL_LIMIT[512];
		char NOTIFY_ONSALE_ITEM_SUC[512];
		char NOTIFY_ONSALE_ITEM_CANNOT[512];
		char NOTIFY_ONSALE_FREE_LIMIT[512];
		char NOTIFY_ONSALE_PUT_SUC[512];
		char NOTIFY_ONSALE_MONEY_LIMIT[512];
		char NOTIFY_MERGE_FASHION_CONDITION_FAIL[512];
		char NOTIFY_ITEM_DESTORY_LIMIT_FAIL[512];
		char NOTIFY_MERGE_XYFASHION_CONDITION_FAIL[512];
		char NOTIFY_SPLIT_BAG_FULL_FAIL[512];
		char NOTIFY_MAIL_RECEIVE_SUC[512];
		char NOTIFY_MAIL_HAVE_RECEIVED[512];
		char NOTIFY_MAIL_DELETE_SUC[512];
		char NOTIFY_MAIL_EXCEED_MAX_LENGTH[512];
		char NOTIFY_MAIL_DELETE_UNRECEIVED[512];
		char NOTIFY_MAIL_NOT_EXIST[512];
		char NOTIFY_BAG_BLANK_NOT_ENOUGH[512];
		char NOTIFY_STALL_STOP[512];
		char NOTIFY_STALL_ITEM_FULL[512];
		char NOTIFY_STALL_NO_ACTION[512];
		char NOTIFY_STALL_NAME_ERROR[512];
		char NOTIFY_CHATTRADE_MYSELF[512];
		char NOTIFY_CHATTRADE_FAIL[512];
		char NOTIFY_CHATTRADE_BAGFULL[512];
		char NOTIFY_CHATTRADE_MONEY_NOTENOUGH[512];
		char NOTIFY_HUICHENGSHI_CISHU[512];
		char NOTIFY_MAP_NOGOD[512];
		char NOTIFY_CAN_NOT_DEPOT[512];
		char NOTIFY_CAN_NOT_DESTORY[512];
		char NOTIFY_SAFEAREA_NO_DROP[512];
		char NOTIFY_MAIL_TITLE[512];
		char NOTIFY_MAIL_CONTENT[512];
		char NOTIFY_ANONYM_NAME[512];
		char NOTIFY_PLAYER_NOT_ONLINE_CHAT[512];
		char NOTIFY_CHANGE_ATTACK_MODE_FAIL[512];
	};
}