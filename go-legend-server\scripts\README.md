# 数据库脚本说明

本目录包含Go传奇服务器的数据库初始化和管理脚本。

## 文件说明

### init.sql
完整的数据库初始化脚本，包含：
- 数据库和表结构创建
- 基础数据插入
- 索引优化
- 视图创建
- 存储过程定义

## 使用方法

### 1. 初始化数据库

#### 方法一：直接使用MySQL命令行
```bash
# 创建数据库并导入结构和数据
mysql -u root -p < scripts/init.sql
```

#### 方法二：分步执行
```bash
# 1. 登录MySQL
mysql -u root -p

# 2. 创建数据库
CREATE DATABASE legend_server DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 3. 使用数据库
USE legend_server;

# 4. 导入脚本
source /path/to/go-legend-server/scripts/init.sql;
```

### 2. 使用迁移工具

```bash
# 编译迁移工具
go build -o migrate cmd/tools/migrate.go

# 运行数据库迁移
./migrate -config configs/gameserver.yaml -action migrate

# 插入测试数据
./migrate -config configs/gameserver.yaml -action seed

# 重置数据库（危险操作！）
./migrate -config configs/gameserver.yaml -action reset
```

## 数据库结构

### 核心表

#### players - 玩家表
存储玩家的基础信息、属性、位置、状态等数据。

#### equipments - 装备表
存储所有装备的详细信息，包括属性、强化等级、宝石等。

#### player_equipments - 玩家装备关联表
记录玩家装备的装备及其位置。

#### monsters - 怪物表
存储怪物的基础信息和属性。

#### skills - 技能表
存储技能的基础信息和属性。

#### player_skills - 玩家技能表
记录玩家学会的技能及其等级。

### 配置表

#### equipment_templates - 装备模板表
装备生成的模板配置。

#### drop_tables - 掉落表
怪物掉落配置。

#### drop_items - 掉落项目表
具体的掉落物品配置。

#### maps - 地图表
游戏地图信息。

#### teleports - 传送点表
地图间的传送点配置。

## 初始数据

脚本会自动插入以下初始数据：

### 地图
- xinshou (新手村)
- biqi (比奇城)
- mengzhong (蒙中)
- chiyue (赤月峡谷)

### 技能
- 战士技能：基础剑术、攻杀剑术、半月弯刀、烈火剑法
- 法师技能：小火球、大火球、雷电术、疾光电影
- 道士技能：治愈术、精神力战法、召唤骷髅、隐身术

### 装备模板
- 各职业武器模板
- 防具模板（头盔、衣服、鞋子）
- 不同品质和等级的装备

### 怪物
- 新手村：鸡、鹿、稻草人
- 蒙中：森林雪人、钳虫、蛾子
- BOSS：沃玛教主、祖玛教主

### 掉落配置
- 低级怪物掉落表
- 中级怪物掉落表
- BOSS掉落表

## 性能优化

### 索引
脚本包含了针对常用查询的索引优化：
- 玩家状态和地图索引
- 装备类型和拥有者索引
- 怪物地图和等级索引
- 技能相关索引

### 视图
提供了常用的视图：
- v_player_details：玩家详细信息
- v_online_players：在线玩家
- v_equipment_stats：装备统计

### 存储过程
- sp_create_character：创建角色的存储过程

## 注意事项

1. **字符集**：使用utf8mb4字符集支持完整的Unicode字符
2. **外键约束**：部分表使用外键约束保证数据完整性
3. **索引优化**：根据游戏查询特点优化了索引
4. **数据类型**：合理选择数据类型以节省存储空间
5. **备份**：在执行重置操作前请备份重要数据

## 故障排除

### 常见问题

1. **字符集问题**
```sql
-- 检查字符集
SHOW VARIABLES LIKE 'character_set%';

-- 设置正确的字符集
SET NAMES utf8mb4;
```

2. **权限问题**
```sql
-- 授予用户权限
GRANT ALL PRIVILEGES ON legend_server.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

3. **外键约束错误**
```sql
-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;
-- 执行操作
-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
```

## 维护建议

1. **定期备份**：建议每日备份数据库
2. **监控性能**：定期检查慢查询日志
3. **索引维护**：根据实际查询情况调整索引
4. **数据清理**：定期清理过期数据

## 扩展

如需添加新的表或修改现有结构，建议：
1. 创建新的迁移脚本
2. 更新相应的Go模型
3. 添加必要的索引
4. 更新相关文档
