package protocol

import (
	"context"
	"fmt"
	"testing"
)

// MockSession 模拟会话
type MockSession struct {
	id       uint64
	userData map[string]interface{}
	messages []Message
	closed   bool
}

func NewMockSession(id uint64) *MockSession {
	return &MockSession{
		id:       id,
		userData: make(map[string]interface{}),
		messages: make([]Message, 0),
		closed:   false,
	}
}

func (s *MockSession) ID() uint64 {
	return s.id
}

func (s *MockSession) SendMessage(msg Message) error {
	if s.closed {
		return fmt.Errorf("session closed")
	}
	s.messages = append(s.messages, msg)
	return nil
}

func (s *MockSession) GetUserData(key string) (interface{}, bool) {
	value, exists := s.userData[key]
	return value, exists
}

func (s *MockSession) SetUserData(key string, value interface{}) {
	s.userData[key] = value
}

func (s *MockSession) Close() {
	s.closed = true
}

func (s *MockSession) RemoteAddr() string {
	return "127.0.0.1:12345"
}

func (s *MockSession) GetMessages() []Message {
	return s.messages
}

func (s *MockSession) ClearMessages() {
	s.messages = s.messages[:0]
}

// TestMessageSerialization 测试消息序列化
func TestMessageSerialization(t *testing.T) {
	tests := []struct {
		name string
		msg  Message
	}{
		{
			name: "AuthenticateRequest",
			msg: &AuthenticateRequest{
				BaseMessage: BaseMessage{Type: ClientGSAuthenticate},
				SessionID:   "test_session_123",
				AuthSeed:    12345,
				PID:         67890,
				IDFA:        "test_idfa",
			},
		},
		{
			name: "ChatMessage",
			msg: &ChatMessage{
				BaseMessage: BaseMessage{Type: ClientGSMapChat},
				SenderID:    1001,
				SenderName:  "TestPlayer",
				Content:     "Hello, World!",
				ChatType:    0,
			},
		},
		{
			name: "MoveMessage",
			msg: &MoveMessage{
				BaseMessage: BaseMessage{Type: ClientGSWalk},
				PlayerID:    1001,
				Direction:   4,
				X:           100,
				Y:           200,
				Tag:         0,
				MoveType:    0,
			},
		},
		{
			name: "AttackMessage",
			msg: &AttackMessage{
				BaseMessage: BaseMessage{Type: ClientGSAttack},
				AttackerID:  1001,
				TargetID:    1002,
				SkillID:     0,
				Damage:      50,
				X:           100,
				Y:           200,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 序列化
			data, err := tt.msg.Serialize()
			if err != nil {
				t.Fatalf("Failed to serialize message: %v", err)
			}

			// 创建新的消息实例
			factory := &MessageFactory{}
			newMsg := factory.CreateMessage(tt.msg.GetType())
			if newMsg == nil {
				t.Fatalf("Failed to create message of type %d", tt.msg.GetType())
			}

			// 反序列化
			err = newMsg.Deserialize(data)
			if err != nil {
				t.Fatalf("Failed to deserialize message: %v", err)
			}

			// 验证消息类型
			if newMsg.GetType() != tt.msg.GetType() {
				t.Errorf("Message type mismatch: expected %d, got %d", tt.msg.GetType(), newMsg.GetType())
			}
		})
	}
}

// TestMessagePacking 测试消息打包和解包
func TestMessagePacking(t *testing.T) {
	msg := &ChatMessage{
		BaseMessage: BaseMessage{Type: ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "TestPlayer",
		Content:     "Hello, World!",
		ChatType:    0,
	}

	// 打包消息
	data, err := PackMessage(msg)
	if err != nil {
		t.Fatalf("Failed to pack message: %v", err)
	}

	// 解包消息
	unpackedMsg, err := UnpackMessage(data)
	if err != nil {
		t.Fatalf("Failed to unpack message: %v", err)
	}

	// 验证消息类型
	if unpackedMsg.GetType() != msg.GetType() {
		t.Errorf("Message type mismatch: expected %d, got %d", msg.GetType(), unpackedMsg.GetType())
	}

	// 验证消息内容
	chatMsg, ok := unpackedMsg.(*ChatMessage)
	if !ok {
		t.Fatalf("Failed to cast message to ChatMessage")
	}

	if chatMsg.SenderID != msg.SenderID {
		t.Errorf("SenderID mismatch: expected %d, got %d", msg.SenderID, chatMsg.SenderID)
	}

	if chatMsg.SenderName != msg.SenderName {
		t.Errorf("SenderName mismatch: expected %s, got %s", msg.SenderName, chatMsg.SenderName)
	}

	if chatMsg.Content != msg.Content {
		t.Errorf("Content mismatch: expected %s, got %s", msg.Content, chatMsg.Content)
	}
}

// TestMessageRouter 测试消息路由器
func TestMessageRouter(t *testing.T) {
	router := NewMessageRouter()
	session := NewMockSession(1001)
	ctx := context.Background()

	// 注册处理器
	handlerCalled := false
	router.RegisterHandler(ClientGSMapChat, func(ctx context.Context, session SessionInterface, msg Message) error {
		handlerCalled = true
		chatMsg, ok := msg.(*ChatMessage)
		if !ok {
			t.Errorf("Expected ChatMessage, got %T", msg)
		}
		if chatMsg.Content != "test message" {
			t.Errorf("Expected 'test message', got '%s'", chatMsg.Content)
		}
		return nil
	})

	// 创建测试消息
	msg := &ChatMessage{
		BaseMessage: BaseMessage{Type: ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "TestPlayer",
		Content:     "test message",
		ChatType:    0,
	}

	// 处理消息
	err := router.HandleMessage(ctx, session, msg)
	if err != nil {
		t.Fatalf("Failed to handle message: %v", err)
	}

	if !handlerCalled {
		t.Error("Handler was not called")
	}
}

// TestMessageValidator 测试消息验证器
func TestMessageValidator(t *testing.T) {
	validator := &DefaultMessageValidator{}

	tests := []struct {
		name    string
		msg     Message
		wantErr bool
	}{
		{
			name: "Valid ChatMessage",
			msg: &ChatMessage{
				BaseMessage: BaseMessage{Type: ClientGSMapChat},
				SenderID:    1001,
				SenderName:  "TestPlayer",
				Content:     "Hello",
				ChatType:    0,
			},
			wantErr: false,
		},
		{
			name: "Invalid ChatMessage - Empty Content",
			msg: &ChatMessage{
				BaseMessage: BaseMessage{Type: ClientGSMapChat},
				SenderID:    1001,
				SenderName:  "TestPlayer",
				Content:     "",
				ChatType:    0,
			},
			wantErr: true,
		},
		{
			name: "Valid MoveMessage",
			msg: &MoveMessage{
				BaseMessage: BaseMessage{Type: ClientGSWalk},
				PlayerID:    1001,
				Direction:   4,
				X:           100,
				Y:           200,
				Tag:         0,
				MoveType:    0,
			},
			wantErr: false,
		},
		{
			name: "Invalid MoveMessage - Negative Position",
			msg: &MoveMessage{
				BaseMessage: BaseMessage{Type: ClientGSWalk},
				PlayerID:    1001,
				Direction:   4,
				X:           -1,
				Y:           200,
				Tag:         0,
				MoveType:    0,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateMessage(tt.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateMessage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestFrameCodec 测试帧编解码器
func TestFrameCodec(t *testing.T) {
	codec := NewDefaultCodec()
	frameCodec := NewFrameCodec(codec)

	// 创建测试消息
	msg1 := &ChatMessage{
		BaseMessage: BaseMessage{Type: ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "Player1",
		Content:     "Message 1",
		ChatType:    0,
	}

	msg2 := &ChatMessage{
		BaseMessage: BaseMessage{Type: ClientGSMapChat},
		SenderID:    1002,
		SenderName:  "Player2",
		Content:     "Message 2",
		ChatType:    0,
	}

	// 编码消息
	data1, err := codec.Encode(msg1)
	if err != nil {
		t.Fatalf("Failed to encode message 1: %v", err)
	}

	data2, err := codec.Encode(msg2)
	if err != nil {
		t.Fatalf("Failed to encode message 2: %v", err)
	}

	// 模拟TCP粘包：将两个消息的数据合并
	combinedData := append(data1, data2...)

	// 写入帧编解码器
	frameCodec.Write(combinedData)

	// 读取第一个消息
	decodedMsg1, err := frameCodec.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read message 1: %v", err)
	}

	// 读取第二个消息
	decodedMsg2, err := frameCodec.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read message 2: %v", err)
	}

	// 验证消息
	chatMsg1, ok := decodedMsg1.(*ChatMessage)
	if !ok {
		t.Fatalf("Failed to cast message 1 to ChatMessage")
	}
	if chatMsg1.Content != "Message 1" {
		t.Errorf("Message 1 content mismatch: expected 'Message 1', got '%s'", chatMsg1.Content)
	}

	chatMsg2, ok := decodedMsg2.(*ChatMessage)
	if !ok {
		t.Fatalf("Failed to cast message 2 to ChatMessage")
	}
	if chatMsg2.Content != "Message 2" {
		t.Errorf("Message 2 content mismatch: expected 'Message 2', got '%s'", chatMsg2.Content)
	}
}

// BenchmarkMessageSerialization 消息序列化性能测试
func BenchmarkMessageSerialization(b *testing.B) {
	msg := &ChatMessage{
		BaseMessage: BaseMessage{Type: ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "TestPlayer",
		Content:     "Hello, World!",
		ChatType:    0,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := msg.Serialize()
		if err != nil {
			b.Fatalf("Failed to serialize message: %v", err)
		}
	}
}

// BenchmarkMessageDeserialization 消息反序列化性能测试
func BenchmarkMessageDeserialization(b *testing.B) {
	msg := &ChatMessage{
		BaseMessage: BaseMessage{Type: ClientGSMapChat},
		SenderID:    1001,
		SenderName:  "TestPlayer",
		Content:     "Hello, World!",
		ChatType:    0,
	}

	data, err := msg.Serialize()
	if err != nil {
		b.Fatalf("Failed to serialize message: %v", err)
	}

	factory := &MessageFactory{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		newMsg := factory.CreateMessage(ClientGSMapChat)
		err := newMsg.Deserialize(data)
		if err != nil {
			b.Fatalf("Failed to deserialize message: %v", err)
		}
	}
}
