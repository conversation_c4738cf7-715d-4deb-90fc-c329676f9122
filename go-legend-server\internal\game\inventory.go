package game

import (
	"sync"
	"time"
)

// Inventory 背包系统
type Inventory struct {
	player *Player
	items  map[int32]*Item // 位置 -> 物品
	mutex  sync.RWMutex
}

// NewInventory 创建背包
func NewInventory(player *Player) *Inventory {
	return &Inventory{
		player: player,
		items:  make(map[int32]*Item),
	}
}

// AddItem 添加物品
func (inv *Inventory) AddItem(item *Item, position int32) bool {
	inv.mutex.Lock()
	defer inv.mutex.Unlock()
	
	// 检查位置是否已被占用
	if _, exists := inv.items[position]; exists {
		return false
	}
	
	inv.items[position] = item
	return true
}

// RemoveItem 移除物品
func (inv *Inventory) RemoveItem(position int32) *Item {
	inv.mutex.Lock()
	defer inv.mutex.Unlock()
	
	item, exists := inv.items[position]
	if exists {
		delete(inv.items, position)
	}
	return item
}

// GetItem 获取物品
func (inv *Inventory) GetItem(position int32) (*Item, bool) {
	inv.mutex.RLock()
	defer inv.mutex.RUnlock()
	
	item, exists := inv.items[position]
	return item, exists
}

// SkillSet 技能系统
type SkillSet struct {
	player *Player
	skills map[int32]*Skill
	mutex  sync.RWMutex
}

// NewSkillSet 创建技能集
func NewSkillSet(player *Player) *SkillSet {
	return &SkillSet{
		player: player,
		skills: make(map[int32]*Skill),
	}
}

// AddSkill 添加技能
func (ss *SkillSet) AddSkill(skill *Skill) {
	ss.mutex.Lock()
	defer ss.mutex.Unlock()
	
	ss.skills[skill.ID] = skill
}

// GetSkill 获取技能
func (ss *SkillSet) GetSkill(skillID int32) (*Skill, bool) {
	ss.mutex.RLock()
	defer ss.mutex.RUnlock()
	
	skill, exists := ss.skills[skillID]
	return skill, exists
}

// BuffSet Buff系统
type BuffSet struct {
	player *Player
	buffs  map[int32]*Buff
	mutex  sync.RWMutex
}

// NewBuffSet 创建Buff集
func NewBuffSet(player *Player) *BuffSet {
	return &BuffSet{
		player: player,
		buffs:  make(map[int32]*Buff),
	}
}

// AddBuff 添加Buff
func (bs *BuffSet) AddBuff(buff *Buff) {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()
	
	bs.buffs[buff.ID] = buff
}

// RemoveBuff 移除Buff
func (bs *BuffSet) RemoveBuff(buffID int32) {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()
	
	delete(bs.buffs, buffID)
}

// Update 更新Buff
func (bs *BuffSet) Update(deltaTime time.Duration) {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()
	
	now := time.Now()
	for buffID, buff := range bs.buffs {
		if now.After(buff.ExpireTime) {
			delete(bs.buffs, buffID)
		}
	}
}

// Item 物品
type Item struct {
	ID       int32
	ItemID   int32
	Count    int32
	Durability int32
	MaxDurability int32
	Properties map[string]int32
}

// Skill 技能
type Skill struct {
	ID       int32
	Level    int32
	Experience int64
	LastUseTime time.Time
}

// Buff 增益/减益效果
type Buff struct {
	ID         int32
	Type       int32
	Value      int32
	Duration   time.Duration
	ExpireTime time.Time
}
