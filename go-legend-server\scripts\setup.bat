@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM Go传奇服务器数据库快速设置脚本 (Windows版本)
REM 使用方法: setup.bat [mysql_user] [mysql_password]

echo === Go传奇服务器数据库快速设置 ===
echo.

REM 设置默认参数
set "MYSQL_USER=%~1"
set "MYSQL_PASSWORD=%~2"
if "%MYSQL_USER%"=="" set "MYSQL_USER=root"
if "%MYSQL_PASSWORD%"=="" set "MYSQL_PASSWORD=123456"
set "DB_NAME=legend_server"

echo 项目目录: %~dp0..
echo MySQL用户: %MYSQL_USER%
echo 数据库名: %DB_NAME%
echo.

REM 检查MySQL是否可用
echo 🔍 检查MySQL连接...
mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL连接失败！请检查用户名和密码
    echo 使用方法: %0 [mysql_user] [mysql_password]
    pause
    exit /b 1
)
echo ✅ MySQL连接成功

REM 检查MySQL版本
echo 🔍 检查MySQL版本...
for /f "skip=1" %%i in ('mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -e "SELECT VERSION();" 2^>nul') do (
    echo ✅ MySQL版本: %%i
    echo %%i | findstr "5.5 5.6" >nul
    if not errorlevel 1 (
        echo ⚠️  检测到较旧的MySQL版本，已优化兼容性
    )
    goto :version_done
)
:version_done

REM 检查数据库是否已存在
echo 🔍 检查数据库是否存在...
mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -e "USE %DB_NAME%;" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  数据库 '%DB_NAME%' 已存在
    set /p "choice=是否要重新创建数据库？这将删除所有现有数据！(y/N): "
    if /i "!choice!"=="y" (
        echo 🗑️  删除现有数据库...
        mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -e "DROP DATABASE IF EXISTS %DB_NAME%;"
        echo ✅ 数据库已删除
    ) else (
        echo ❌ 操作已取消
        pause
        exit /b 0
    )
)

REM 执行初始化脚本
echo 🚀 开始初始化数据库...
mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% < "%~dp0init.sql"
if errorlevel 1 (
    echo ❌ 数据库初始化失败！
    pause
    exit /b 1
)
echo ✅ 数据库初始化完成！

REM 验证数据库结构
echo 🔍 验证数据库结构...
for /f "skip=1" %%i in ('mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -D%DB_NAME% -e "SHOW TABLES;" 2^>nul') do (
    set /a table_count+=1
)
if !table_count! gtr 10 (
    echo ✅ 数据库表创建成功 ^(共 !table_count! 个表^)
) else (
    echo ❌ 数据库表创建可能有问题
)

REM 检查初始数据
echo 🔍 检查初始数据...
for /f "skip=1" %%i in ('mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -D%DB_NAME% -e "SELECT COUNT(*) FROM maps;" 2^>nul') do set "maps_count=%%i"
for /f "skip=1" %%i in ('mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -D%DB_NAME% -e "SELECT COUNT(*) FROM skills;" 2^>nul') do set "skills_count=%%i"
for /f "skip=1" %%i in ('mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -D%DB_NAME% -e "SELECT COUNT(*) FROM monsters;" 2^>nul') do set "monsters_count=%%i"

echo ✅ 地图数据: %maps_count% 个地图
echo ✅ 技能数据: %skills_count% 个技能
echo ✅ 怪物数据: %monsters_count% 个怪物

REM 检查配置文件
echo 🔧 检查配置文件...
set "CONFIG_FILE=%~dp0..\configs\gameserver.yaml"
if exist "%CONFIG_FILE%" (
    findstr /c:"database: \"legend_server\"" "%CONFIG_FILE%" >nul
    if not errorlevel 1 (
        echo ✅ 配置文件数据库名称正确
    ) else (
        echo ⚠️  配置文件中的数据库名称可能需要更新
        echo 请确保配置文件中的数据库名称为: %DB_NAME%
    )
    
    findstr /c:"username: \"%MYSQL_USER%\"" "%CONFIG_FILE%" >nul
    if not errorlevel 1 (
        echo ✅ 配置文件用户名正确
    ) else (
        echo ⚠️  请检查配置文件中的MySQL用户名
    )
) else (
    echo ⚠️  配置文件不存在: %CONFIG_FILE%
)

REM 编译和测试迁移工具
echo 🔨 编译迁移工具...
cd /d "%~dp0.."
go build -o migrate.exe cmd\tools\migrate.go >nul 2>&1
if not errorlevel 1 (
    echo ✅ 迁移工具编译成功
    
    REM 测试迁移工具
    echo 🧪 测试迁移工具...
    migrate.exe -config configs\gameserver.yaml -action migrate >nul 2>&1
    if not errorlevel 1 (
        echo ✅ 迁移工具测试成功
    ) else (
        echo ⚠️  迁移工具测试失败，可能需要检查配置
    )
) else (
    echo ⚠️  迁移工具编译失败，请检查Go环境
)

REM 显示下一步操作
echo.
echo === 设置完成 ===
echo ✅ 数据库初始化成功！
echo.
echo 下一步操作:
echo 1. 检查配置文件: configs\gameserver.yaml
echo 2. 启动游戏服务器: go run cmd\gameserver\main.go
echo 3. 插入测试数据: migrate.exe -action seed
echo.
echo 数据库管理命令:
echo - 查看数据库: mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -D%DB_NAME%
echo - 重置数据库: migrate.exe -action reset
echo - 插入测试数据: migrate.exe -action seed
echo.
echo 🎉 祝你游戏开发愉快！
echo.
pause
