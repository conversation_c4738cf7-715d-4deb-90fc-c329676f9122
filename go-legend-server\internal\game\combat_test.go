package game

import (
	"testing"
	"time"

	"github.com/legend-server/go-legend-server/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCombatSystem_ProcessAttack(t *testing.T) {
	// 初始化日志
	logger.InitLogger("debug", "console", "")
	
	// 创建战斗系统
	combatSystem := NewCombatSystem()
	
	// 创建测试玩家
	attacker := createTestPlayer(1, "Warrior", JobWarrior, 10)
	target := createTestPlayer(2, "Wizard", JobWizard, 10)
	
	// 设置相同地图和位置
	attacker.MapID = "test_map"
	attacker.X = 100
	attacker.Y = 100
	target.MapID = "test_map"
	target.X = 102
	target.Y = 102
	
	// 设置攻击模式
	attacker.AttackMode = AttackModeAll
	
	// 测试正常攻击
	result, err := combatSystem.ProcessAttack(attacker, target, 0)
	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, attacker.ID, result.AttackerID)
	assert.Equal(t, target.ID, result.TargetID)
	assert.Greater(t, result.Damage, int32(0))
	assert.False(t, result.IsMiss)
}

func TestCombatSystem_ValidateAttack(t *testing.T) {
	combatSystem := NewCombatSystem()
	
	// 创建测试玩家
	attacker := createTestPlayer(1, "Warrior", JobWarrior, 10)
	target := createTestPlayer(2, "Wizard", JobWizard, 10)
	
	// 测试攻击自己
	err := combatSystem.validateAttack(attacker, attacker)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot attack self")
	
	// 测试死亡玩家攻击
	attacker.HP = 0
	err = combatSystem.validateAttack(attacker, target)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "attacker is dead")
	
	// 恢复生命值
	attacker.HP = attacker.MaxHP
	
	// 测试攻击死亡目标
	target.HP = 0
	err = combatSystem.validateAttack(attacker, target)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "target is dead")
	
	// 恢复目标生命值
	target.HP = target.MaxHP
	
	// 测试距离过远
	attacker.MapID = "map1"
	target.MapID = "map2"
	err = combatSystem.validateAttack(attacker, target)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "target out of range")
}

func TestCombatSystem_CalculateHit(t *testing.T) {
	combatSystem := NewCombatSystem()
	
	// 创建测试玩家
	attacker := createTestPlayer(1, "Warrior", JobWarrior, 10)
	target := createTestPlayer(2, "Wizard", JobWizard, 10)
	
	// 设置极高命中率
	attacker.Accuracy = 1000
	target.Agility = 1
	
	hitCount := 0
	totalTests := 100
	
	for i := 0; i < totalTests; i++ {
		if combatSystem.calculateHit(attacker, target) {
			hitCount++
		}
	}
	
	// 高命中率应该命中大部分攻击
	assert.Greater(t, hitCount, totalTests*8/10)
}

func TestCombatSystem_CalculateDamage(t *testing.T) {
	combatSystem := NewCombatSystem()
	
	// 创建测试玩家
	attacker := createTestPlayer(1, "Warrior", JobWarrior, 10)
	target := createTestPlayer(2, "Wizard", JobWizard, 10)
	
	// 测试普通攻击
	damage := combatSystem.calculateDamage(attacker, target, 0)
	assert.Greater(t, damage, int32(0))
	
	// 测试技能攻击
	skill := &Skill{
		ID:     1,
		Level:  1,
		Damage: 50,
	}
	attacker.skills.AddSkill(skill)
	
	skillDamage := combatSystem.calculateDamage(attacker, target, 1)
	assert.Greater(t, skillDamage, damage) // 技能伤害应该更高
}

func TestSkillSystem_UseSkill(t *testing.T) {
	// 初始化日志
	logger.InitLogger("debug", "console", "")
	
	skillSystem := NewSkillSystem()
	
	// 创建测试玩家
	player := createTestPlayer(1, "Wizard", JobWizard, 10)
	
	// 学习技能
	skill := &Skill{
		ID:          1,
		Level:       1,
		Experience:  0,
		LastUseTime: time.Time{},
		Damage:      30,
		MPCost:      10,
		Range:       3,
		Cooldown:    1000,
	}
	player.skills.AddSkill(skill)
	
	// 测试正常使用技能
	err := skillSystem.UseSkill(player, 1, 100, 100, 0)
	assert.NoError(t, err)
	assert.Equal(t, int32(90), player.MP) // MP应该减少
	
	// 测试冷却时间
	err = skillSystem.UseSkill(player, 1, 100, 100, 0)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cooldown")
	
	// 测试MP不足
	player.MP = 5
	skill.LastUseTime = time.Time{} // 重置冷却时间
	err = skillSystem.UseSkill(player, 1, 100, 100, 0)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not enough MP")
}

func TestSkillSystem_AddSkillExperience(t *testing.T) {
	skillSystem := NewSkillSystem()
	
	// 创建测试玩家
	player := createTestPlayer(1, "Wizard", JobWizard, 10)
	
	// 学习技能
	skill := &Skill{
		ID:         1,
		Level:      1,
		Experience: 0,
	}
	player.skills.AddSkill(skill)
	
	// 添加经验
	skillSystem.addSkillExperience(player, 1, 100)
	
	// 检查经验增加
	updatedSkill, exists := player.skills.GetSkill(1)
	assert.True(t, exists)
	assert.Equal(t, int64(100), updatedSkill.Experience)
	
	// 添加足够经验升级
	skillSystem.addSkillExperience(player, 1, 50) // 总共150，超过1级所需的50
	
	// 检查升级
	updatedSkill, exists = player.skills.GetSkill(1)
	assert.True(t, exists)
	assert.Equal(t, int32(2), updatedSkill.Level)
	assert.Equal(t, int64(0), updatedSkill.Experience) // 升级后经验重置
}

func TestPlayer_LearnSkill(t *testing.T) {
	// 创建测试玩家
	player := createTestPlayer(1, "Wizard", JobWizard, 10)
	
	// 学习新技能
	err := player.LearnSkill(1)
	assert.NoError(t, err)
	
	// 检查技能是否学会
	skill, exists := player.skills.GetSkill(1)
	assert.True(t, exists)
	assert.Equal(t, int32(1), skill.ID)
	assert.Equal(t, int32(1), skill.Level)
	
	// 尝试重复学习
	err = player.LearnSkill(1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already learned")
}

// createTestPlayer 创建测试玩家
func createTestPlayer(id uint64, name string, job JobType, level int32) *Player {
	player := &Player{
		ID:       id,
		CharName: name,
		Job:      job,
		Level:    level,
		HP:       100,
		MaxHP:    100,
		MP:       100,
		MaxMP:    100,
		Attack:   50,
		Defense:  30,
		MagicAttack: 40,
		MagicDefense: 25,
		Accuracy: 20,
		Agility:  15,
		AttackMode: AttackModeAll,
		logger:   logger.GetLogger(),
	}
	
	// 初始化子系统
	player.inventory = NewInventory(player)
	player.skills = NewSkillSet(player)
	player.buffs = NewBuffSet(player)
	
	return player
}
