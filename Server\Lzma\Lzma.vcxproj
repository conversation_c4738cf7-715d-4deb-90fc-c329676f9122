﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="111|Win32">
      <Configuration>111</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{35848D03-7191-4B48-A4EE-C196BAA812E2}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>Lzma</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='111|Win32'">
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>Debug\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IntDir>Debug\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>Release\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IntDir>Release\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation>true</BrowseInformation>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Bscmake>
      <PreserveSbr>true</PreserveSbr>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BrowseInformation>true</BrowseInformation>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Bscmake>
      <PreserveSbr>true</PreserveSbr>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C\7zBuf.c" />
    <ClCompile Include="C\7zBuf2.c" />
    <ClCompile Include="C\7zCrc.c" />
    <ClCompile Include="C\7zFile.c" />
    <ClCompile Include="C\7zStream.c" />
    <ClCompile Include="C\Alloc.c" />
    <ClCompile Include="C\Archive\7z\7zAlloc.c" />
    <ClCompile Include="C\Archive\7z\7zDecode.c" />
    <ClCompile Include="C\Archive\7z\7zExtract.c" />
    <ClCompile Include="C\Archive\7z\7zHeader.c" />
    <ClCompile Include="C\Archive\7z\7zIn.c" />
    <ClCompile Include="C\Archive\7z\7zItem.c" />
    <ClCompile Include="C\Bcj2.c" />
    <ClCompile Include="C\Bra.c" />
    <ClCompile Include="C\Bra86.c" />
    <ClCompile Include="C\BraIA64.c" />
    <ClCompile Include="C\LzFind.c" />
    <ClCompile Include="C\LzFindMt.c" />
    <ClCompile Include="C\LzmaDec.c" />
    <ClCompile Include="C\LzmaEnc.c" />
    <ClCompile Include="C\LzmaLib.c" />
    <ClCompile Include="C\Threads.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C\7zBuf.h" />
    <ClInclude Include="C\7zCrc.h" />
    <ClInclude Include="C\7zFile.h" />
    <ClInclude Include="C\7zVersion.h" />
    <ClInclude Include="C\Alloc.h" />
    <ClInclude Include="C\Archive\7z\7zAlloc.h" />
    <ClInclude Include="C\Archive\7z\7zDecode.h" />
    <ClInclude Include="C\Archive\7z\7zExtract.h" />
    <ClInclude Include="C\Archive\7z\7zHeader.h" />
    <ClInclude Include="C\Archive\7z\7zIn.h" />
    <ClInclude Include="C\Archive\7z\7zItem.h" />
    <ClInclude Include="C\Bcj2.h" />
    <ClInclude Include="C\Bra.h" />
    <ClInclude Include="C\CpuArch.h" />
    <ClInclude Include="C\LzFind.h" />
    <ClInclude Include="C\LzFindMt.h" />
    <ClInclude Include="C\LzHash.h" />
    <ClInclude Include="C\LzmaDec.h" />
    <ClInclude Include="C\LzmaEnc.h" />
    <ClInclude Include="C\LzmaLib.h" />
    <ClInclude Include="C\Threads.h" />
    <ClInclude Include="C\Types.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>