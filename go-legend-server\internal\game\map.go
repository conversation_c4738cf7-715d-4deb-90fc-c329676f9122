package game

import (
	"sync"
	"time"

	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// GameMap 游戏地图
type GameMap struct {
	ID     string
	Name   string
	Width  int32
	Height int32

	// 地图上的玩家
	players     map[uint64]*Player
	playerMutex sync.RWMutex

	// 地图上的NPC
	npcs     map[int32]*NPC
	npcMutex sync.RWMutex

	// 地图上的怪物
	monsters     map[int32]*Monster
	monsterMutex sync.RWMutex

	// 地图上的物品
	items     map[int32]*DropItem
	itemMutex sync.RWMutex

	logger *zap.Logger
}

// NewGameMap 创建游戏地图
func NewGameMap(id, name string) *GameMap {
	return &GameMap{
		ID:       id,
		Name:     name,
		Width:    1000, // 默认地图大小
		Height:   1000,
		players:  make(map[uint64]*Player),
		npcs:     make(map[int32]*NPC),
		monsters: make(map[int32]*Monster),
		items:    make(map[int32]*DropItem),
		logger:   logger.GetLogger(),
	}
}

// AddPlayer 添加玩家到地图
func (m *GameMap) AddPlayer(player *Player) {
	m.playerMutex.Lock()
	defer m.playerMutex.Unlock()

	m.players[player.ID] = player

	m.logger.Debug("Player added to map",
		zap.String("map_id", m.ID),
		zap.Uint64("player_id", player.ID),
		zap.String("char_name", player.CharName))
}

// RemovePlayer 从地图移除玩家
func (m *GameMap) RemovePlayer(playerID uint64) {
	m.playerMutex.Lock()
	defer m.playerMutex.Unlock()

	if player, exists := m.players[playerID]; exists {
		delete(m.players, playerID)

		m.logger.Debug("Player removed from map",
			zap.String("map_id", m.ID),
			zap.Uint64("player_id", playerID),
			zap.String("char_name", player.CharName))
	}
}

// GetPlayer 获取地图上的玩家
func (m *GameMap) GetPlayer(playerID uint64) (*Player, bool) {
	m.playerMutex.RLock()
	defer m.playerMutex.RUnlock()

	player, exists := m.players[playerID]
	return player, exists
}

// GetPlayersInRange 获取指定范围内的玩家
func (m *GameMap) GetPlayersInRange(x, y, range_ int32) []*Player {
	m.playerMutex.RLock()
	defer m.playerMutex.RUnlock()

	var result []*Player
	for _, player := range m.players {
		if m.isInRange(player.X, player.Y, x, y, range_) {
			result = append(result, player)
		}
	}
	return result
}

// GetAllPlayers 获取地图上所有玩家
func (m *GameMap) GetAllPlayers() []*Player {
	m.playerMutex.RLock()
	defer m.playerMutex.RUnlock()

	players := make([]*Player, 0, len(m.players))
	for _, player := range m.players {
		players = append(players, player)
	}
	return players
}

// GetPlayerCount 获取地图上玩家数量
func (m *GameMap) GetPlayerCount() int {
	m.playerMutex.RLock()
	defer m.playerMutex.RUnlock()

	return len(m.players)
}

// AddNPC 添加NPC到地图
func (m *GameMap) AddNPC(npc *NPC) {
	m.npcMutex.Lock()
	defer m.npcMutex.Unlock()

	m.npcs[npc.ID] = npc
}

// RemoveNPC 从地图移除NPC
func (m *GameMap) RemoveNPC(npcID int32) {
	m.npcMutex.Lock()
	defer m.npcMutex.Unlock()

	delete(m.npcs, npcID)
}

// GetNPC 获取NPC
func (m *GameMap) GetNPC(npcID int32) (*NPC, bool) {
	m.npcMutex.RLock()
	defer m.npcMutex.RUnlock()

	npc, exists := m.npcs[npcID]
	return npc, exists
}

// AddMonster 添加怪物到地图
func (m *GameMap) AddMonster(monster *Monster) {
	m.monsterMutex.Lock()
	defer m.monsterMutex.Unlock()

	m.monsters[monster.ID] = monster
}

// RemoveMonster 从地图移除怪物
func (m *GameMap) RemoveMonster(monsterID int32) {
	m.monsterMutex.Lock()
	defer m.monsterMutex.Unlock()

	delete(m.monsters, monsterID)
}

// GetMonster 获取怪物
func (m *GameMap) GetMonster(monsterID int32) (*Monster, bool) {
	m.monsterMutex.RLock()
	defer m.monsterMutex.RUnlock()

	monster, exists := m.monsters[monsterID]
	return monster, exists
}

// AddDropItem 添加掉落物品到地图
func (m *GameMap) AddDropItem(item *DropItem) {
	m.itemMutex.Lock()
	defer m.itemMutex.Unlock()

	m.items[item.ID] = item
}

// RemoveDropItem 从地图移除掉落物品
func (m *GameMap) RemoveDropItem(itemID int32) {
	m.itemMutex.Lock()
	defer m.itemMutex.Unlock()

	delete(m.items, itemID)
}

// GetDropItem 获取掉落物品
func (m *GameMap) GetDropItem(itemID int32) (*DropItem, bool) {
	m.itemMutex.RLock()
	defer m.itemMutex.RUnlock()

	item, exists := m.items[itemID]
	return item, exists
}

// Update 更新地图
func (m *GameMap) Update(deltaTime time.Duration) {
	// 更新怪物
	m.updateMonsters(deltaTime)

	// 更新掉落物品
	m.updateDropItems(deltaTime)
}

// updateMonsters 更新怪物
func (m *GameMap) updateMonsters(deltaTime time.Duration) {
	m.monsterMutex.RLock()
	monsters := make([]*Monster, 0, len(m.monsters))
	for _, monster := range m.monsters {
		monsters = append(monsters, monster)
	}
	m.monsterMutex.RUnlock()

	for _, monster := range monsters {
		monster.Update(deltaTime)
	}
}

// updateDropItems 更新掉落物品
func (m *GameMap) updateDropItems(deltaTime time.Duration) {
	m.itemMutex.Lock()
	defer m.itemMutex.Unlock()

	now := time.Now()
	for itemID, item := range m.items {
		// 检查物品是否过期
		if now.Sub(item.DropTime) > 5*time.Minute {
			delete(m.items, itemID)
		}
	}
}

// isInRange 检查是否在指定范围内
func (m *GameMap) isInRange(x1, y1, x2, y2, range_ int32) bool {
	dx := x1 - x2
	dy := y1 - y2
	distance := dx*dx + dy*dy
	return distance <= range_*range_
}

// IsValidPosition 检查位置是否有效
func (m *GameMap) IsValidPosition(x, y int32) bool {
	return x >= 0 && x < m.Width && y >= 0 && y < m.Height
}

// BroadcastToMap 广播消息到地图上所有玩家
func (m *GameMap) BroadcastToMap(msg interface{}) {
	// TODO: 实现消息广播
}

// BroadcastToRange 广播消息到指定范围内的玩家
func (m *GameMap) BroadcastToRange(x, y, range_ int32, msg interface{}) {
	// TODO: 实现范围广播
}

// NPC NPC对象
type NPC struct {
	ID        int32
	Name      string
	X         int32
	Y         int32
	Direction int32
	ScriptID  string
}

// DropItem 掉落物品
type DropItem struct {
	ID       int32
	ItemID   int32
	X        int32
	Y        int32
	Count    int32
	DropTime time.Time
	OwnerID  uint64 // 物品归属玩家ID，0表示所有人都可以拾取
}
