LIBRARY		LIBMYSQLD
DESCRIPTION	'MySQL 5.1 Embedded Server Library'
VERSION		5.1
EXPORTS
	mysql_thread_end
	mysql_thread_init
	myodbc_remove_escape
	mysql_affected_rows
	mysql_autocommit
	mysql_change_user
	mysql_character_set_name
	mysql_close
	mysql_commit
	mysql_data_seek
	mysql_debug
	mysql_disable_rpl_parse
	mysql_dump_debug_info
	mysql_enable_rpl_parse
	mysql_eof
	mysql_errno
	mysql_error
	mysql_escape_string
	mysql_hex_string
	mysql_fetch_field
	mysql_fetch_field_direct
	mysql_fetch_fields
	mysql_fetch_lengths
	mysql_fetch_row
	mysql_field_count
	mysql_field_seek
	mysql_field_tell
	mysql_free_result
	mysql_get_character_set_info
	mysql_get_client_info
	mysql_get_host_info
	mysql_get_proto_info
	mysql_get_server_info
	mysql_get_client_version
	mysql_get_ssl_cipher
	mysql_info
	mysql_init
	mysql_insert_id
	mysql_kill
	mysql_set_server_option
	mysql_list_dbs
	mysql_list_fields
	mysql_list_processes
	mysql_list_tables
	mysql_more_results
	mysql_next_result
	mysql_num_fields
	mysql_num_rows
	mysql_options
	mysql_ping
	mysql_query
	mysql_read_query_result
	mysql_real_connect
	mysql_real_escape_string
	mysql_real_query
	mysql_refresh
	mysql_rollback
	mysql_row_seek
	mysql_row_tell
	mysql_rpl_parse_enabled
	mysql_rpl_probe
	mysql_select_db
	mysql_send_query
	mysql_shutdown
	mysql_ssl_set
	mysql_stat
	mysql_store_result
	mysql_sqlstate
	mysql_thread_id
	mysql_thread_safe
	mysql_use_result
	mysql_warning_count
	mysql_server_end
	mysql_server_init
	get_tty_password
	mysql_get_server_version
	mysql_set_character_set
	mysql_sqlstate
	mysql_get_parameters
	mysql_stmt_bind_param
	mysql_stmt_bind_result
	mysql_stmt_execute
	mysql_stmt_fetch
	mysql_stmt_fetch_column
	mysql_stmt_param_count
	mysql_stmt_param_metadata
	mysql_stmt_result_metadata
	mysql_stmt_send_long_data
	mysql_stmt_affected_rows
	mysql_stmt_close
	mysql_stmt_reset
	mysql_stmt_data_seek
	mysql_stmt_errno
	mysql_stmt_error
	mysql_stmt_free_result
	mysql_stmt_num_rows
	mysql_stmt_row_seek
	mysql_stmt_row_tell
	mysql_stmt_store_result
	mysql_stmt_sqlstate
	mysql_stmt_prepare
	mysql_stmt_init
	mysql_stmt_insert_id
	mysql_stmt_attr_get
	mysql_stmt_attr_set
	mysql_stmt_field_count
