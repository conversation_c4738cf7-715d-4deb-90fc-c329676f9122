package protocol

import (
	"bytes"
	"fmt"
	"io"
	"sync"
)

// Codec 协议编解码器接口
type Codec interface {
	Encode(msg Message) ([]byte, error)
	Decode(data []byte) (Message, error)
	DecodeHeader(data []byte) (*MessageHeader, error)
}

// DefaultCodec 默认协议编解码器
type DefaultCodec struct {
	factory *MessageFactory
}

// NewDefaultCodec 创建默认编解码器
func NewDefaultCodec() *DefaultCodec {
	return &DefaultCodec{
		factory: &MessageFactory{},
	}
}

// Encode 编码消息
func (c *DefaultCodec) Encode(msg Message) ([]byte, error) {
	return PackMessage(msg)
}

// Decode 解码消息
func (c *DefaultCodec) Decode(data []byte) (Message, error) {
	return UnpackMessage(data)
}

// DecodeHeader 解码消息头
func (c *DefaultCodec) DecodeHeader(data []byte) (*MessageHeader, error) {
	return DeserializeHeader(data)
}

// FrameCodec 帧编解码器，处理TCP粘包问题
type FrameCodec struct {
	codec  Codec
	buffer *bytes.Buffer
}

// NewFrameCodec 创建帧编解码器
func NewFrameCodec(codec Codec) *FrameCodec {
	return &FrameCodec{
		codec:  codec,
		buffer: bytes.NewBuffer(make([]byte, 0, 4096)),
	}
}

// Write 写入数据到缓冲区
func (f *FrameCodec) Write(data []byte) {
	f.buffer.Write(data)
}

// ReadMessage 从缓冲区读取完整消息
func (f *FrameCodec) ReadMessage() (Message, error) {
	for {
		// 检查是否有足够的数据读取消息头
		if f.buffer.Len() < HeaderSize {
			return nil, io.ErrShortBuffer
		}

		// 读取消息头但不移动读取位置
		headerData := f.buffer.Bytes()[:HeaderSize]
		header, err := f.codec.DecodeHeader(headerData)
		if err != nil {
			return nil, fmt.Errorf("failed to decode header: %w", err)
		}

		// 检查消息长度是否合法
		if header.Length < HeaderSize || header.Length > 65535 {
			return nil, fmt.Errorf("invalid message length: %d", header.Length)
		}

		// 检查是否有足够的数据读取完整消息
		if f.buffer.Len() < int(header.Length) {
			return nil, io.ErrShortBuffer
		}

		// 读取完整消息
		messageData := make([]byte, header.Length)
		n, err := f.buffer.Read(messageData)
		if err != nil {
			return nil, fmt.Errorf("failed to read message data: %w", err)
		}
		if n != int(header.Length) {
			return nil, fmt.Errorf("incomplete message read: expected %d, got %d", header.Length, n)
		}

		// 解码消息
		msg, err := f.codec.Decode(messageData)
		if err != nil {
			return nil, fmt.Errorf("failed to decode message: %w", err)
		}

		return msg, nil
	}
}

// HasCompleteMessage 检查是否有完整消息
func (f *FrameCodec) HasCompleteMessage() bool {
	if f.buffer.Len() < HeaderSize {
		return false
	}

	headerData := f.buffer.Bytes()[:HeaderSize]
	header, err := DeserializeHeader(headerData)
	if err != nil {
		return false
	}

	return f.buffer.Len() >= int(header.Length)
}

// Reset 重置缓冲区
func (f *FrameCodec) Reset() {
	f.buffer.Reset()
}

// BufferLen 获取缓冲区长度
func (f *FrameCodec) BufferLen() int {
	return f.buffer.Len()
}

// CompressedCodec 压缩编解码器
type CompressedCodec struct {
	baseCodec Codec
	threshold int // 压缩阈值，超过此大小才压缩
}

// NewCompressedCodec 创建压缩编解码器
func NewCompressedCodec(baseCodec Codec, threshold int) *CompressedCodec {
	return &CompressedCodec{
		baseCodec: baseCodec,
		threshold: threshold,
	}
}

// Encode 编码消息（带压缩）
func (c *CompressedCodec) Encode(msg Message) ([]byte, error) {
	data, err := c.baseCodec.Encode(msg)
	if err != nil {
		return nil, err
	}

	// 如果数据大小超过阈值，进行压缩
	if len(data) > c.threshold {
		// TODO: 实现压缩逻辑（如gzip、zlib等）
		// 这里暂时返回原始数据
	}

	return data, nil
}

// Decode 解码消息（带解压缩）
func (c *CompressedCodec) Decode(data []byte) (Message, error) {
	// TODO: 检查是否为压缩数据，如果是则解压缩
	return c.baseCodec.Decode(data)
}

// DecodeHeader 解码消息头
func (c *CompressedCodec) DecodeHeader(data []byte) (*MessageHeader, error) {
	return c.baseCodec.DecodeHeader(data)
}

// EncryptedCodec 加密编解码器
type EncryptedCodec struct {
	baseCodec Codec
	key       []byte
}

// NewEncryptedCodec 创建加密编解码器
func NewEncryptedCodec(baseCodec Codec, key []byte) *EncryptedCodec {
	return &EncryptedCodec{
		baseCodec: baseCodec,
		key:       key,
	}
}

// Encode 编码消息（带加密）
func (c *EncryptedCodec) Encode(msg Message) ([]byte, error) {
	data, err := c.baseCodec.Encode(msg)
	if err != nil {
		return nil, err
	}

	// TODO: 实现加密逻辑
	// 这里暂时返回原始数据
	return data, nil
}

// Decode 解码消息（带解密）
func (c *EncryptedCodec) Decode(data []byte) (Message, error) {
	// TODO: 实现解密逻辑
	return c.baseCodec.Decode(data)
}

// DecodeHeader 解码消息头
func (c *EncryptedCodec) DecodeHeader(data []byte) (*MessageHeader, error) {
	return c.baseCodec.DecodeHeader(data)
}

// CodecChain 编解码器链
type CodecChain struct {
	codecs []Codec
}

// NewCodecChain 创建编解码器链
func NewCodecChain(codecs ...Codec) *CodecChain {
	return &CodecChain{
		codecs: codecs,
	}
}

// Encode 编码消息
func (c *CodecChain) Encode(msg Message) ([]byte, error) {
	if len(c.codecs) == 0 {
		return nil, fmt.Errorf("no codecs in chain")
	}

	// 使用第一个编解码器编码
	return c.codecs[0].Encode(msg)
}

// Decode 解码消息
func (c *CodecChain) Decode(data []byte) (Message, error) {
	if len(c.codecs) == 0 {
		return nil, fmt.Errorf("no codecs in chain")
	}

	// 使用第一个编解码器解码
	return c.codecs[0].Decode(data)
}

// DecodeHeader 解码消息头
func (c *CodecChain) DecodeHeader(data []byte) (*MessageHeader, error) {
	if len(c.codecs) == 0 {
		return nil, fmt.Errorf("no codecs in chain")
	}

	return c.codecs[0].DecodeHeader(data)
}

// MessagePool 消息对象池
type MessagePool struct {
	pools map[MessageType]*sync.Pool
	mutex sync.RWMutex
}

// NewMessagePool 创建消息对象池
func NewMessagePool() *MessagePool {
	return &MessagePool{
		pools: make(map[MessageType]*sync.Pool),
	}
}

// Get 获取消息对象
func (p *MessagePool) Get(msgType MessageType) Message {
	p.mutex.RLock()
	pool, exists := p.pools[msgType]
	p.mutex.RUnlock()

	if !exists {
		// 创建新的对象池
		p.mutex.Lock()
		if pool, exists = p.pools[msgType]; !exists {
			pool = &sync.Pool{
				New: func() interface{} {
					factory := &MessageFactory{}
					return factory.CreateMessage(msgType)
				},
			}
			p.pools[msgType] = pool
		}
		p.mutex.Unlock()
	}

	if msg := pool.Get(); msg != nil {
		return msg.(Message)
	}

	// 如果池中没有对象，创建新的
	factory := &MessageFactory{}
	return factory.CreateMessage(msgType)
}

// Put 归还消息对象
func (p *MessagePool) Put(msg Message) {
	if msg == nil {
		return
	}

	msgType := msg.GetType()
	p.mutex.RLock()
	pool, exists := p.pools[msgType]
	p.mutex.RUnlock()

	if exists {
		// 重置消息对象
		// TODO: 实现消息重置逻辑
		pool.Put(msg)
	}
}
