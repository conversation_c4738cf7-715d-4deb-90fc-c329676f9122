package network

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"github.com/legend-server/go-legend-server/internal/protocol"
	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// SessionState 会话状态
type SessionState int32

const (
	SessionStateConnected SessionState = iota
	SessionStateAuthenticated
	SessionStateInGame
	SessionStateClosed
)

// Session 网络会话
type Session struct {
	id         uint64
	conn       net.Conn
	server     *Server
	state      int32
	closed     int32
	sendChan   chan []byte
	closeChan  chan struct{}
	wg         sync.WaitGroup
	logger     *zap.Logger
	lastActive time.Time
	userData   sync.Map // 用户自定义数据

	// 读写缓冲区
	readBuffer  []byte
	writeBuffer []byte
	reader      *bufio.Reader
	writer      *bufio.Writer

	// 协议处理
	frameCodec *protocol.FrameCodec
}

// NewSession 创建新会话
func NewSession(id uint64, conn net.Conn, server *Server) *Session {
	codec := protocol.NewDefaultCodec()
	frameCodec := protocol.NewFrameCodec(codec)

	return &Session{
		id:          id,
		conn:        conn,
		server:      server,
		state:       int32(SessionStateConnected),
		sendChan:    make(chan []byte, 1000), // 发送队列
		closeChan:   make(chan struct{}),
		logger:      logger.GetLogger(),
		lastActive:  time.Now(),
		readBuffer:  make([]byte, 4096),
		writeBuffer: make([]byte, 4096),
		reader:      bufio.NewReader(conn),
		writer:      bufio.NewWriter(conn),
		frameCodec:  frameCodec,
	}
}

// ID 获取会话ID
func (s *Session) ID() uint64 {
	return s.id
}

// RemoteAddr 获取远程地址
func (s *Session) RemoteAddr() string {
	return s.conn.RemoteAddr().String()
}

// State 获取会话状态
func (s *Session) State() SessionState {
	return SessionState(atomic.LoadInt32(&s.state))
}

// SetState 设置会话状态
func (s *Session) SetState(state SessionState) {
	atomic.StoreInt32(&s.state, int32(state))
}

// IsClosed 检查会话是否已关闭
func (s *Session) IsClosed() bool {
	return atomic.LoadInt32(&s.closed) == 1
}

// Start 启动会话处理
func (s *Session) Start(ctx context.Context) error {
	if s.IsClosed() {
		return fmt.Errorf("session already closed")
	}

	// 设置连接超时
	if s.server.config.ReadTimeout > 0 {
		s.conn.SetReadDeadline(time.Now().Add(s.server.config.ReadTimeout))
	}
	if s.server.config.WriteTimeout > 0 {
		s.conn.SetWriteDeadline(time.Now().Add(s.server.config.WriteTimeout))
	}

	// 启动读写goroutine
	s.wg.Add(2)
	go s.readLoop(ctx)
	go s.writeLoop(ctx)

	// 等待会话结束
	s.wg.Wait()
	return nil
}

// Close 关闭会话
func (s *Session) Close() {
	if !atomic.CompareAndSwapInt32(&s.closed, 0, 1) {
		return
	}

	s.SetState(SessionStateClosed)
	close(s.closeChan)
	s.conn.Close()
}

// SendMessage 发送消息
func (s *Session) SendMessage(msg protocol.Message) error {
	if s.IsClosed() {
		return fmt.Errorf("session closed")
	}

	data, err := protocol.PackMessage(msg)
	if err != nil {
		return fmt.Errorf("failed to pack message: %w", err)
	}

	return s.SendRaw(data)
}

// SendRaw 发送原始数据
func (s *Session) SendRaw(data []byte) error {
	if s.IsClosed() {
		return fmt.Errorf("session closed")
	}

	select {
	case s.sendChan <- data:
		return nil
	case <-s.closeChan:
		return fmt.Errorf("session closed")
	default:
		return fmt.Errorf("send queue full")
	}
}

// readLoop 读取循环
func (s *Session) readLoop(ctx context.Context) {
	defer s.wg.Done()
	defer s.Close()

	buffer := make([]byte, 4096)

	for {
		select {
		case <-ctx.Done():
			return
		case <-s.closeChan:
			return
		default:
		}

		// 读取数据
		n, err := s.reader.Read(buffer)
		if err != nil {
			if err != io.EOF && !s.IsClosed() {
				s.logger.Error("Failed to read data",
					zap.Uint64("session_id", s.id),
					zap.Error(err))
			}
			return
		}

		if n > 0 {
			// 写入帧编解码器
			s.frameCodec.Write(buffer[:n])

			// 处理所有完整的消息
			for s.frameCodec.HasCompleteMessage() {
				msg, err := s.frameCodec.ReadMessage()
				if err != nil {
					s.logger.Error("Failed to read message from frame codec",
						zap.Uint64("session_id", s.id),
						zap.Error(err))
					return
				}

				// 更新活跃时间
				s.lastActive = time.Now()

				// 处理消息
				if err := s.server.HandleMessage(s, msg); err != nil {
					s.logger.Error("Failed to handle message",
						zap.Uint64("session_id", s.id),
						zap.Uint16("msg_type", uint16(msg.GetType())),
						zap.Error(err))
				}
			}
		}
	}
}

// writeLoop 写入循环
func (s *Session) writeLoop(ctx context.Context) {
	defer s.wg.Done()
	defer s.Close()

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-s.closeChan:
			return
		case data := <-s.sendChan:
			if err := s.writeData(data); err != nil {
				s.logger.Error("Failed to write data",
					zap.Uint64("session_id", s.id),
					zap.Error(err))
				return
			}
		case <-ticker.C:
			// 定期刷新缓冲区
			if err := s.writer.Flush(); err != nil {
				s.logger.Error("Failed to flush writer",
					zap.Uint64("session_id", s.id),
					zap.Error(err))
				return
			}
		}
	}
}

// writeData 写入数据
func (s *Session) writeData(data []byte) error {
	if _, err := s.writer.Write(data); err != nil {
		return err
	}
	return s.writer.Flush()
}

// SetUserData 设置用户数据
func (s *Session) SetUserData(key string, value interface{}) {
	s.userData.Store(key, value)
}

// GetUserData 获取用户数据
func (s *Session) GetUserData(key string) (interface{}, bool) {
	return s.userData.Load(key)
}

// DeleteUserData 删除用户数据
func (s *Session) DeleteUserData(key string) {
	s.userData.Delete(key)
}

// GetLastActive 获取最后活跃时间
func (s *Session) GetLastActive() time.Time {
	return s.lastActive
}

// IsTimeout 检查是否超时
func (s *Session) IsTimeout(timeout time.Duration) bool {
	return time.Since(s.lastActive) > timeout
}
