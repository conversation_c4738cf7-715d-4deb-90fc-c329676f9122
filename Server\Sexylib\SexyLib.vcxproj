﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="111|Win32">
      <Configuration>111</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A05D8661-B05D-4539-8D24-C21CF3DD12CE}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='111|Win32'">
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Debug\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Release\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\Vandor;..\..\tools\mirex\zlib\;..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <BrowseInformation>true</BrowseInformation>
    </ClCompile>
    <Lib>
      <OutputFile>$(OutDir)SexyLib.lib</OutputFile>
    </Lib>
    <Bscmake>
      <PreserveSbr>true</PreserveSbr>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>..\..\Vandor;..\..\tools\mirex\zlib\;..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <BrowseInformation>true</BrowseInformation>
    </ClCompile>
    <Lib>
      <OutputFile>$(OutDir)SexyLib.lib</OutputFile>
    </Lib>
    <Bscmake>
      <PreserveSbr>true</PreserveSbr>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="abc.cpp" />
    <ClCompile Include="Application.cpp" />
    <ClCompile Include="Cryptography.cpp" />
    <ClCompile Include="DBMssql.cpp" />
    <ClCompile Include="DBMysql.cpp" />
    <ClCompile Include="DebugCrash.cpp" />
    <ClCompile Include="EventQueue.cpp" />
    <ClCompile Include="EventQueueThread.cpp" />
    <ClCompile Include="Logger.cpp" />
    <ClCompile Include="M.cpp" />
    <ClCompile Include="md5.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
      </PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="MemoryBlock.cpp" />
    <ClCompile Include="MemoryPool.cpp" />
    <ClCompile Include="MonitorIOCP.cpp" />
    <ClCompile Include="Performance.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">_PERFORMANCE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="RecvBuffer.cpp" />
    <ClCompile Include="ServerMain.cpp" />
    <ClCompile Include="ServiceNT.cpp" />
    <ClCompile Include="SoftwareKey.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="StreamBlock.cpp" />
    <ClCompile Include="StreamFix.cpp" />
    <ClCompile Include="TCPAccept.cpp" />
    <ClCompile Include="TCPAcceptEventQueue.cpp" />
    <ClCompile Include="TCPClient.cpp" />
    <ClCompile Include="TCPConnect.cpp" />
    <ClCompile Include="TCPConnectEventQueue.cpp" />
    <ClCompile Include="TCPSession.cpp" />
    <ClCompile Include="TCPSessionManager.cpp" />
    <ClCompile Include="TCPSessionMini.cpp" />
    <ClCompile Include="TimerFix.cpp" />
    <ClCompile Include="ToolsConsole.cpp" />
    <ClCompile Include="ToolsHTTP.cpp" />
    <ClCompile Include="ToolsMemFile.cpp" />
    <ClCompile Include="ToolsPath.cpp" />
    <ClCompile Include="ToolsString.cpp" />
    <ClCompile Include="UDPSession.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="abc.h" />
    <ClInclude Include="leudgrid\ActionBase.h" />
    <ClInclude Include="leudgrid\Application.h" />
    <ClInclude Include="leudgrid\Cryptography.h" />
    <ClInclude Include="leudgrid\DBMssql.h" />
    <ClInclude Include="leudgrid\DBMysql.h" />
    <ClInclude Include="leudgrid\DebugCrash.h" />
    <ClInclude Include="leudgrid\DebugLeak.h" />
    <ClInclude Include="leudgrid\EventBase.h" />
    <ClInclude Include="leudgrid\EventQueue.h" />
    <ClInclude Include="leudgrid\EventQueueThread.h" />
    <ClInclude Include="leudgrid\Logger.h" />
    <ClInclude Include="leudgrid\Performance.h" />
    <ClInclude Include="leudgrid\TCPSessionMini.h" />
    <ClInclude Include="leudgrid\ToolsConsole.h" />
    <ClInclude Include="leudgrid\ToolsDeque.h" />
    <ClInclude Include="leudgrid\ToolsMemFile.h" />
    <ClInclude Include="M.h" />
    <ClInclude Include="md5.h" />
    <ClInclude Include="leudgrid\MemoryBlock.h" />
    <ClInclude Include="leudgrid\MemoryBlockPRD.h" />
    <ClInclude Include="leudgrid\MemoryPool.h" />
    <ClInclude Include="leudgrid\MonitorIOCP.h" />
    <ClInclude Include="leudgrid\RecvBuffer.h" />
    <ClInclude Include="leudgrid\ServerMain.h" />
    <ClInclude Include="leudgrid\ServiceNT.h" />
    <ClInclude Include="leudgrid\SoftwareKey.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="leudgrid\StreamBase.h" />
    <ClInclude Include="leudgrid\StreamBaseEx.h" />
    <ClInclude Include="leudgrid\StreamBlock.h" />
    <ClInclude Include="leudgrid\StreamBlockPRD.h" />
    <ClInclude Include="leudgrid\StreamFix.h" />
    <ClInclude Include="leudgrid\TCPAccept.h" />
    <ClInclude Include="leudgrid\TCPAcceptEventQueue.h" />
    <ClInclude Include="leudgrid\TCPClient.h" />
    <ClInclude Include="leudgrid\TCPConnect.h" />
    <ClInclude Include="leudgrid\TCPConnectEventQueue.h" />
    <ClInclude Include="leudgrid\TCPRecver.h" />
    <ClInclude Include="leudgrid\TCPSender.h" />
    <ClInclude Include="leudgrid\TCPSession.h" />
    <ClInclude Include="leudgrid\TCPSessionManager.h" />
    <ClInclude Include="leudgrid\TimerFix.h" />
    <ClInclude Include="leudgrid\ToolsHTTP.h" />
    <ClInclude Include="leudgrid\ToolsPath.h" />
    <ClInclude Include="leudgrid\ToolsString.h" />
    <ClInclude Include="leudgrid\ToolsThread.h" />
    <ClInclude Include="leudgrid\ToolsUIDGen.h" />
    <ClInclude Include="leudgrid\UDPRecver.h" />
    <ClInclude Include="leudgrid\UDPSender.h" />
    <ClInclude Include="leudgrid\UDPSession.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="leudgrid\StreamBlock.inl" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>