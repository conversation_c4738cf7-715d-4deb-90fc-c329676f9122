package protocol

import (
	"context"
	"fmt"
	"sync"

	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// MessageHandler 消息处理器接口
type MessageHandler interface {
	HandleMessage(ctx context.Context, session SessionInterface, msg Message) error
}

// SessionInterface 会话接口
type SessionInterface interface {
	ID() uint64
	SendMessage(msg Message) error
	GetUserData(key string) (interface{}, bool)
	SetUserData(key string, value interface{})
	Close()
	RemoteAddr() string
}

// HandlerFunc 消息处理函数类型
type HandlerFunc func(ctx context.Context, session SessionInterface, msg Message) error

// MessageRouter 消息路由器
type MessageRouter struct {
	handlers map[MessageType]HandlerFunc
	mutex    sync.RWMutex
	logger   *zap.Logger
}

// NewMessageRouter 创建消息路由器
func NewMessageRouter() *MessageRouter {
	return &MessageRouter{
		handlers: make(map[MessageType]HandlerFunc),
		logger:   logger.GetLogger(),
	}
}

// RegisterHandler 注册消息处理器
func (r *MessageRouter) RegisterHandler(msgType MessageType, handler HandlerFunc) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.handlers[msgType] = handler
	r.logger.Debug("Registered message handler", zap.Uint16("msg_type", uint16(msgType)))
}

// UnregisterHandler 注销消息处理器
func (r *MessageRouter) UnregisterHandler(msgType MessageType) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	delete(r.handlers, msgType)
	r.logger.Debug("Unregistered message handler", zap.Uint16("msg_type", uint16(msgType)))
}

// HandleMessage 处理消息
func (r *MessageRouter) HandleMessage(ctx context.Context, session SessionInterface, msg Message) error {
	r.mutex.RLock()
	handler, exists := r.handlers[msg.GetType()]
	r.mutex.RUnlock()

	if !exists {
		r.logger.Warn("No handler for message type",
			zap.Uint16("msg_type", uint16(msg.GetType())),
			zap.Uint64("session_id", session.ID()))
		return fmt.Errorf("no handler for message type: %d", msg.GetType())
	}

	r.logger.Debug("Handling message",
		zap.Uint16("msg_type", uint16(msg.GetType())),
		zap.Uint64("session_id", session.ID()))

	return handler(ctx, session, msg)
}

// GetHandlerCount 获取处理器数量
func (r *MessageRouter) GetHandlerCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return len(r.handlers)
}

// MessageProcessor 消息处理器
type MessageProcessor struct {
	router  *MessageRouter
	factory *MessageFactory
	logger  *zap.Logger
}

// NewMessageProcessor 创建消息处理器
func NewMessageProcessor() *MessageProcessor {
	return &MessageProcessor{
		router:  NewMessageRouter(),
		factory: &MessageFactory{},
		logger:  logger.GetLogger(),
	}
}

// RegisterHandler 注册消息处理器
func (p *MessageProcessor) RegisterHandler(msgType MessageType, handler HandlerFunc) {
	p.router.RegisterHandler(msgType, handler)
}

// ProcessMessage 处理原始消息数据
func (p *MessageProcessor) ProcessMessage(ctx context.Context, session SessionInterface, data []byte) error {
	// 解包消息
	msg, err := UnpackMessage(data)
	if err != nil {
		p.logger.Error("Failed to unpack message",
			zap.Uint64("session_id", session.ID()),
			zap.Error(err))
		return fmt.Errorf("failed to unpack message: %w", err)
	}

	// 处理消息
	return p.router.HandleMessage(ctx, session, msg)
}

// SendMessage 发送消息
func (p *MessageProcessor) SendMessage(session SessionInterface, msg Message) error {
	return session.SendMessage(msg)
}

// BroadcastMessage 广播消息到多个会话
func (p *MessageProcessor) BroadcastMessage(sessions []SessionInterface, msg Message) {
	for _, session := range sessions {
		if err := session.SendMessage(msg); err != nil {
			p.logger.Error("Failed to send broadcast message",
				zap.Uint64("session_id", session.ID()),
				zap.Error(err))
		}
	}
}

// MessageValidator 消息验证器接口
type MessageValidator interface {
	ValidateMessage(msg Message) error
}

// DefaultMessageValidator 默认消息验证器
type DefaultMessageValidator struct{}

// ValidateMessage 验证消息
func (v *DefaultMessageValidator) ValidateMessage(msg Message) error {
	switch m := msg.(type) {
	case *ChatMessage:
		return v.validateChatMessage(m)
	case *MoveMessage:
		return v.validateMoveMessage(m)
	case *AttackMessage:
		return v.validateAttackMessage(m)
	default:
		return nil
	}
}

// validateChatMessage 验证聊天消息
func (v *DefaultMessageValidator) validateChatMessage(msg *ChatMessage) error {
	if len(msg.Content) == 0 {
		return fmt.Errorf("chat content cannot be empty")
	}
	if len(msg.Content) > 1000 {
		return fmt.Errorf("chat content too long: %d", len(msg.Content))
	}
	if len(msg.SenderName) == 0 {
		return fmt.Errorf("sender name cannot be empty")
	}
	return nil
}

// validateMoveMessage 验证移动消息
func (v *DefaultMessageValidator) validateMoveMessage(msg *MoveMessage) error {
	if msg.X < 0 || msg.Y < 0 {
		return fmt.Errorf("invalid position: (%d, %d)", msg.X, msg.Y)
	}
	if msg.Direction < 0 || msg.Direction > 7 {
		return fmt.Errorf("invalid direction: %d", msg.Direction)
	}
	return nil
}

// validateAttackMessage 验证攻击消息
func (v *DefaultMessageValidator) validateAttackMessage(msg *AttackMessage) error {
	if msg.AttackerID <= 0 {
		return fmt.Errorf("invalid attacker id: %d", msg.AttackerID)
	}
	if msg.TargetID <= 0 {
		return fmt.Errorf("invalid target id: %d", msg.TargetID)
	}
	if msg.Damage < 0 {
		return fmt.Errorf("invalid damage: %d", msg.Damage)
	}
	return nil
}

// MessageMiddleware 消息中间件接口
type MessageMiddleware interface {
	Process(ctx context.Context, session SessionInterface, msg Message, next HandlerFunc) error
}

// LoggingMiddleware 日志中间件
type LoggingMiddleware struct {
	logger *zap.Logger
}

// NewLoggingMiddleware 创建日志中间件
func NewLoggingMiddleware() *LoggingMiddleware {
	return &LoggingMiddleware{
		logger: logger.GetLogger(),
	}
}

// Process 处理消息
func (m *LoggingMiddleware) Process(ctx context.Context, session SessionInterface, msg Message, next HandlerFunc) error {
	m.logger.Debug("Processing message",
		zap.Uint16("msg_type", uint16(msg.GetType())),
		zap.Uint64("session_id", session.ID()),
		zap.String("remote_addr", session.RemoteAddr()))

	err := next(ctx, session, msg)

	if err != nil {
		m.logger.Error("Message processing failed",
			zap.Uint16("msg_type", uint16(msg.GetType())),
			zap.Uint64("session_id", session.ID()),
			zap.Error(err))
	} else {
		m.logger.Debug("Message processed successfully",
			zap.Uint16("msg_type", uint16(msg.GetType())),
			zap.Uint64("session_id", session.ID()))
	}

	return err
}

// ValidationMiddleware 验证中间件
type ValidationMiddleware struct {
	validator MessageValidator
	logger    *zap.Logger
}

// NewValidationMiddleware 创建验证中间件
func NewValidationMiddleware(validator MessageValidator) *ValidationMiddleware {
	return &ValidationMiddleware{
		validator: validator,
		logger:    logger.GetLogger(),
	}
}

// Process 处理消息
func (m *ValidationMiddleware) Process(ctx context.Context, session SessionInterface, msg Message, next HandlerFunc) error {
	// 验证消息
	if err := m.validator.ValidateMessage(msg); err != nil {
		m.logger.Warn("Message validation failed",
			zap.Uint16("msg_type", uint16(msg.GetType())),
			zap.Uint64("session_id", session.ID()),
			zap.Error(err))
		return fmt.Errorf("message validation failed: %w", err)
	}

	return next(ctx, session, msg)
}

// MiddlewareChain 中间件链
type MiddlewareChain struct {
	middlewares []MessageMiddleware
}

// NewMiddlewareChain 创建中间件链
func NewMiddlewareChain(middlewares ...MessageMiddleware) *MiddlewareChain {
	return &MiddlewareChain{
		middlewares: middlewares,
	}
}

// Process 处理消息
func (c *MiddlewareChain) Process(ctx context.Context, session SessionInterface, msg Message, handler HandlerFunc) error {
	if len(c.middlewares) == 0 {
		return handler(ctx, session, msg)
	}

	// 构建中间件链
	next := handler
	for i := len(c.middlewares) - 1; i >= 0; i-- {
		middleware := c.middlewares[i]
		currentNext := next
		next = func(ctx context.Context, session SessionInterface, msg Message) error {
			return middleware.Process(ctx, session, msg, currentNext)
		}
	}

	return next(ctx, session, msg)
}
