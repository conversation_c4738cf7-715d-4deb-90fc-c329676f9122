package game

import (
	"testing"
	"time"

	"github.com/legend-server/go-legend-server/pkg/config"
	"github.com/legend-server/go-legend-server/pkg/logger"
)

func TestDropSystem(t *testing.T) {
	// 初始化日志
	cfg := &config.Config{
		Log: config.LogConfig{
			Level:  "debug",
			Format: "json",
		},
	}
	logger.Initialize(cfg)

	// 创建掉落系统
	dropSystem := NewDropSystem()

	t.Run("TestEquipmentGeneration", func(t *testing.T) {
		// 测试装备生成
		equipment, err := dropSystem.GenerateEquipment(1001, 15)
		if err != nil {
			t.Fatalf("Failed to generate equipment: %v", err)
		}

		if equipment == nil {
			t.Fatal("Generated equipment is nil")
		}

		if equipment.Name != "新手剑" {
			t.Errorf("Expected equipment name '新手剑', got '%s'", equipment.Name)
		}

		if equipment.Type != EquipTypeWeapon {
			t.Errorf("Expected equipment type %d, got %d", EquipTypeWeapon, equipment.Type)
		}

		t.Logf("Generated equipment: %s (Quality: %d, Attack: %d, Enhance: +%d)",
			equipment.Name, equipment.Quality, equipment.Attack, equipment.EnhanceLevel)
	})

	t.Run("TestDropProcessing", func(t *testing.T) {
		// 测试掉落处理
		droppedItems := dropSystem.ProcessDrop(1, 10, JobWarrior, "map001", 100, 100)

		if len(droppedItems) == 0 {
			t.Log("No items dropped (this is normal due to random chance)")
			return
		}

		t.Logf("Dropped %d items", len(droppedItems))

		for i, item := range droppedItems {
			t.Logf("Item %d: Type=%d, ItemID=%d, Quantity=%d",
				i+1, item.Type, item.ItemID, item.Quantity)

			if item.Type == DropTypeEquipment && item.Equipment != nil {
				t.Logf("  Equipment: %s (Quality: %d)",
					item.Equipment.Name, item.Equipment.Quality)
			}
		}
	})

	t.Run("TestItemPickup", func(t *testing.T) {
		// 测试物品拾取
		droppedItems := dropSystem.ProcessDrop(1, 10, JobWarrior, "map001", 100, 100)

		if len(droppedItems) == 0 {
			t.Skip("No items dropped, skipping pickup test")
		}

		item := droppedItems[0]
		playerID := uint64(12345)

		// 拾取物品
		pickedItem, err := dropSystem.PickupItem(item.ID, playerID)
		if err != nil {
			t.Fatalf("Failed to pickup item: %v", err)
		}

		if pickedItem.ID != item.ID {
			t.Errorf("Expected picked item ID %d, got %d", item.ID, pickedItem.ID)
		}

		// 尝试再次拾取同一物品（应该失败）
		_, err = dropSystem.PickupItem(item.ID, playerID)
		if err == nil {
			t.Error("Expected error when picking up already picked item")
		}

		t.Logf("Successfully picked up item %d", pickedItem.ID)
	})

	t.Run("TestItemExpiration", func(t *testing.T) {
		// 测试物品过期
		droppedItems := dropSystem.ProcessDrop(1, 10, JobWarrior, "map001", 100, 100)

		if len(droppedItems) == 0 {
			t.Skip("No items dropped, skipping expiration test")
		}

		item := droppedItems[0]

		// 手动设置过期时间为过去
		item.ExpireTime = time.Now().Add(-1 * time.Minute)

		playerID := uint64(12345)

		// 尝试拾取过期物品
		_, err := dropSystem.PickupItem(item.ID, playerID)
		if err == nil {
			t.Error("Expected error when picking up expired item")
		}

		t.Logf("Correctly rejected pickup of expired item: %v", err)
	})

	t.Run("TestCleanupExpiredItems", func(t *testing.T) {
		// 测试清理过期物品
		initialCount := len(dropSystem.droppedItems)

		// 创建一些掉落物品
		droppedItems := dropSystem.ProcessDrop(1, 10, JobWarrior, "map001", 100, 100)

		if len(droppedItems) > 0 {
			// 手动设置所有物品为过期
			for _, item := range droppedItems {
				item.ExpireTime = time.Now().Add(-1 * time.Minute)
			}

			// 清理过期物品
			dropSystem.CleanupExpiredItems()

			// 检查物品是否被清理
			finalCount := len(dropSystem.droppedItems)
			if finalCount != initialCount {
				t.Errorf("Expected %d items after cleanup, got %d", initialCount, finalCount)
			}

			t.Logf("Successfully cleaned up expired items")
		}
	})

	t.Run("TestDifferentDropTables", func(t *testing.T) {
		// 测试不同掉落表
		tables := []int32{1, 2, 3} // 低级、中级、Boss掉落表
		levels := []int32{5, 15, 30}

		for i, tableID := range tables {
			level := levels[i]
			droppedItems := dropSystem.ProcessDrop(tableID, level, JobWarrior, "map001", 100, 100)

			t.Logf("Drop table %d (level %d): %d items dropped", tableID, level, len(droppedItems))

			for j, item := range droppedItems {
				if j >= 3 { // 只显示前3个物品
					break
				}
				t.Logf("  Item: Type=%d, ItemID=%d, Quantity=%d",
					item.Type, item.ItemID, item.Quantity)
			}
		}
	})

	t.Run("TestEquipmentQualityRandomization", func(t *testing.T) {
		// 测试装备品质随机化
		qualityCounts := make(map[EquipmentQuality]int)

		// 生成多个装备测试品质分布
		for i := 0; i < 50; i++ {
			equipment, err := dropSystem.GenerateEquipment(1002, 20) // 精钢剑
			if err != nil {
				continue
			}
			qualityCounts[equipment.Quality]++
		}

		t.Log("Equipment quality distribution:")
		for quality, count := range qualityCounts {
			t.Logf("  Quality %d: %d items", quality, count)
		}

		// 应该有不同品质的装备
		if len(qualityCounts) < 2 {
			t.Log("Warning: Low quality variation (this might be normal due to random chance)")
		}
	})
}

func TestDropSystemIntegration(t *testing.T) {
	// 初始化日志
	cfg := &config.Config{
		Log: config.LogConfig{
			Level:  "debug",
			Format: "json",
		},
	}
	logger.Initialize(cfg)

	// 创建测试环境
	dropSystem := NewDropSystem()
	combatSystem := NewCombatSystem()

	// 创建测试玩家
	player := NewPlayer("test_account", "测试玩家", JobWarrior, GenderMale)
	player.Level = 15
	player.Attack = 50
	player.Defense = 30
	player.Accuracy = 50 // 设置命中率
	player.HP = 1000     // 设置足够的生命值
	player.MaxHP = 1000

	// 创建测试怪物
	monster := NewMonster(1001, "测试怪物", 10, "map001", 100, 100)
	monster.SetDropSystem(dropSystem)

	t.Run("TestMonsterKillDrop", func(t *testing.T) {
		// 攻击怪物直到死亡
		attackCount := 0
		for monster.IsAlive() && attackCount < 100 { // 防止无限循环
			result, err := combatSystem.ProcessAttackMonster(player, monster, 0)
			if err != nil {
				t.Fatalf("Failed to attack monster: %v", err)
			}

			t.Logf("Attack result: Damage=%d, Miss=%v, Dead=%v",
				result.Damage, result.IsMiss, result.IsDead)

			if result.IsDead {
				t.Log("Monster killed!")
				break
			}

			attackCount++
		}

		if attackCount >= 100 {
			t.Fatal("Too many attacks, monster should be dead by now")
		}

		// 检查是否有掉落物品
		droppedItems := dropSystem.GetAllDroppedItems()
		if len(droppedItems) > 0 {
			t.Logf("Monster dropped %d items", len(droppedItems))
			for _, item := range droppedItems {
				t.Logf("  Dropped item %d: Type=%d, ItemID=%d",
					item.ID, item.Type, item.ItemID)
			}
		} else {
			t.Log("No items dropped (this is normal due to random chance)")
		}
	})
}
