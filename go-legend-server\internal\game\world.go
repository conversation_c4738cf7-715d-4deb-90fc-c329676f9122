package game

import (
	"context"
	"sync"
	"time"

	"github.com/legend-server/go-legend-server/internal/config"
	"github.com/legend-server/go-legend-server/internal/database"
	"github.com/legend-server/go-legend-server/pkg/logger"
	"go.uber.org/zap"
)

// GameWorld 游戏世界
type GameWorld struct {
	config   *config.GameConfig
	database *database.Database
	logger   *zap.Logger
	
	// 玩家管理
	players     map[uint64]*Player
	playerMutex sync.RWMutex
	
	// 地图管理
	maps     map[string]*GameMap
	mapMutex sync.RWMutex
	
	// 运行状态
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
	
	// 定时器
	saveTimer *time.Ticker
}

// NewGameWorld 创建游戏世界
func NewGameWorld(cfg *config.GameConfig, db *database.Database) *GameWorld {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &GameWorld{
		config:   cfg,
		database: db,
		logger:   logger.GetLogger(),
		players:  make(map[uint64]*Player),
		maps:     make(map[string]*GameMap),
		ctx:      ctx,
		cancel:   cancel,
	}
}

// Start 启动游戏世界
func (w *GameWorld) Start() error {
	w.logger.Info("Starting game world...")
	
	w.running = true
	
	// 初始化地图
	if err := w.initMaps(); err != nil {
		return err
	}
	
	// 启动定时保存
	w.saveTimer = time.NewTicker(w.config.SaveInterval)
	w.wg.Add(1)
	go w.saveLoop()
	
	// 启动更新循环
	w.wg.Add(1)
	go w.updateLoop()
	
	w.logger.Info("Game world started successfully")
	return nil
}

// Stop 停止游戏世界
func (w *GameWorld) Stop() error {
	w.logger.Info("Stopping game world...")
	
	w.running = false
	w.cancel()
	
	// 停止定时器
	if w.saveTimer != nil {
		w.saveTimer.Stop()
	}
	
	// 等待所有goroutine结束
	w.wg.Wait()
	
	// 保存所有玩家数据
	w.saveAllPlayers()
	
	w.logger.Info("Game world stopped")
	return nil
}

// AddPlayer 添加玩家
func (w *GameWorld) AddPlayer(player *Player) {
	w.playerMutex.Lock()
	defer w.playerMutex.Unlock()
	
	w.players[player.ID] = player
	player.SetGameWorld(w)
	
	w.logger.Info("Player added to world",
		zap.Uint64("player_id", player.ID),
		zap.String("char_name", player.CharName))
}

// RemovePlayer 移除玩家
func (w *GameWorld) RemovePlayer(playerID uint64) {
	w.playerMutex.Lock()
	defer w.playerMutex.Unlock()
	
	if player, exists := w.players[playerID]; exists {
		delete(w.players, playerID)
		
		w.logger.Info("Player removed from world",
			zap.Uint64("player_id", playerID),
			zap.String("char_name", player.CharName))
	}
}

// GetPlayer 获取玩家
func (w *GameWorld) GetPlayer(playerID uint64) (*Player, bool) {
	w.playerMutex.RLock()
	defer w.playerMutex.RUnlock()
	
	player, exists := w.players[playerID]
	return player, exists
}

// GetPlayerByName 根据名称获取玩家
func (w *GameWorld) GetPlayerByName(charName string) (*Player, bool) {
	w.playerMutex.RLock()
	defer w.playerMutex.RUnlock()
	
	for _, player := range w.players {
		if player.CharName == charName {
			return player, true
		}
	}
	return nil, false
}

// GetOnlinePlayerCount 获取在线玩家数量
func (w *GameWorld) GetOnlinePlayerCount() int {
	w.playerMutex.RLock()
	defer w.playerMutex.RUnlock()
	
	return len(w.players)
}

// GetAllPlayers 获取所有玩家
func (w *GameWorld) GetAllPlayers() []*Player {
	w.playerMutex.RLock()
	defer w.playerMutex.RUnlock()
	
	players := make([]*Player, 0, len(w.players))
	for _, player := range w.players {
		players = append(players, player)
	}
	return players
}

// GetMap 获取地图
func (w *GameWorld) GetMap(mapID string) (*GameMap, bool) {
	w.mapMutex.RLock()
	defer w.mapMutex.RUnlock()
	
	gameMap, exists := w.maps[mapID]
	return gameMap, exists
}

// initMaps 初始化地图
func (w *GameWorld) initMaps() error {
	w.logger.Info("Initializing maps...")
	
	// 创建新手村地图
	startMap := NewGameMap(w.config.StartMap, "新手村")
	w.maps[w.config.StartMap] = startMap
	
	// TODO: 从配置文件或数据库加载更多地图
	
	w.logger.Info("Maps initialized", zap.Int("map_count", len(w.maps)))
	return nil
}

// updateLoop 更新循环
func (w *GameWorld) updateLoop() {
	defer w.wg.Done()
	
	ticker := time.NewTicker(100 * time.Millisecond) // 10 FPS
	defer ticker.Stop()
	
	lastUpdate := time.Now()
	
	for {
		select {
		case <-w.ctx.Done():
			return
		case now := <-ticker.C:
			deltaTime := now.Sub(lastUpdate)
			lastUpdate = now
			
			// 更新所有玩家
			w.updatePlayers(deltaTime)
			
			// 更新所有地图
			w.updateMaps(deltaTime)
		}
	}
}

// updatePlayers 更新所有玩家
func (w *GameWorld) updatePlayers(deltaTime time.Duration) {
	w.playerMutex.RLock()
	players := make([]*Player, 0, len(w.players))
	for _, player := range w.players {
		players = append(players, player)
	}
	w.playerMutex.RUnlock()
	
	for _, player := range players {
		player.Update(deltaTime)
	}
}

// updateMaps 更新所有地图
func (w *GameWorld) updateMaps(deltaTime time.Duration) {
	w.mapMutex.RLock()
	maps := make([]*GameMap, 0, len(w.maps))
	for _, gameMap := range w.maps {
		maps = append(maps, gameMap)
	}
	w.mapMutex.RUnlock()
	
	for _, gameMap := range maps {
		gameMap.Update(deltaTime)
	}
}

// saveLoop 保存循环
func (w *GameWorld) saveLoop() {
	defer w.wg.Done()
	
	for {
		select {
		case <-w.ctx.Done():
			return
		case <-w.saveTimer.C:
			w.saveAllPlayers()
		}
	}
}

// saveAllPlayers 保存所有玩家数据
func (w *GameWorld) saveAllPlayers() {
	w.playerMutex.RLock()
	players := make([]*Player, 0, len(w.players))
	for _, player := range w.players {
		players = append(players, player)
	}
	w.playerMutex.RUnlock()
	
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	for _, player := range players {
		if err := player.Save(ctx, w.database); err != nil {
			w.logger.Error("Failed to save player data",
				zap.Uint64("player_id", player.ID),
				zap.String("char_name", player.CharName),
				zap.Error(err))
		}
	}
	
	w.logger.Debug("Saved all player data", zap.Int("player_count", len(players)))
}

// BroadcastMessage 广播消息到所有玩家
func (w *GameWorld) BroadcastMessage(msg interface{}) {
	// TODO: 实现消息广播
}

// BroadcastToMap 广播消息到指定地图的所有玩家
func (w *GameWorld) BroadcastToMap(mapID string, msg interface{}) {
	// TODO: 实现地图消息广播
}

// GetStats 获取游戏世界统计信息
func (w *GameWorld) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"running":       w.running,
		"player_count":  w.GetOnlinePlayerCount(),
		"map_count":     len(w.maps),
		"exp_multiplier": w.config.ExpMultiplier,
		"money_multiplier": w.config.MoneyMultiplier,
	}
}
